import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*", // Allow images from all domains
      },
      {
        protocol: "http",
        hostname: "*", // Allow images from all domains
      },
    ],
    // Disable optimization for domains that are unreachable
    unoptimized: process.env.NODE_ENV === 'development',
  },
};

export default nextConfig;
