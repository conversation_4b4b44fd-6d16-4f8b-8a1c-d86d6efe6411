export {};

declare global {
  interface Window {
    enkrypted?: {
      isConnected: boolean;
      name: string | null;
      address: string | null;
      lastConnectionAt: Date | null;
      connect: () => Promise<string>;
      signMessage: (msg: string) => Promise<string>;
      sendTransaction: (tx: any) => Promise<string>;
      disconnect: () => Promise<string>;
      getAccount: () => {
        address: string | null;
        name: string | null;
        lastConnectionAt: Date | null;
        isConnected: boolean;
      };
    };
  }
}
