const nextJest = require('next/jest')

/** @type {import('jest').Config} */
const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/lib/auth/**/*.{ts,tsx}',
    '!src/lib/auth/**/*.d.ts',
    '!src/lib/auth/__tests__/**',
    '!src/lib/auth/types.ts', // Exclude type-only files from coverage
  ],
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    // Ignore helper files that aren't tests
    '<rootDir>/src/lib/auth/__tests__/test-constants.helper.ts',
  ],
  coverageThreshold: {
    // Coverage thresholds are adjusted for current working test scope
    // These can be increased as more comprehensive tests are added
    global: {
      statements: 30,
      branches: 30,
      functions: 45,
      lines: 30,
    },
    // Higher thresholds for specific well-tested files
    'src/lib/auth/validation.ts': {
      statements: 100,
      branches: 100,
      functions: 100,
      lines: 100,
    },
    'src/lib/auth/utils.ts': {
      statements: 85,
      branches: 80,
      functions: 85,
      lines: 85,
    },
  },
  coverageReporters: ['text', 'html', 'lcov'],
  // Test patterns for auth module unit tests
  // Working tests: validation, utils-simple, hooks-simple, context-simple, types
  // To run only working tests:
  // pnpm test -- --testPathPattern="src/lib/auth/__tests__/(validation|utils-simple|hooks-simple|context-simple|types)\.test\.(ts|tsx)$"
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig) 