/**
 * Application Constants
 * General app configuration and settings
 */

// App Information
export const APP_INFO = {
  NAME: 'BNRY dApps',
  DESCRIPTION: 'Discover and explore the best decentralized applications on the blockchain',
  VERSION: '1.0.0',
  AUTHOR: 'BNRY Team',
  KEYWORDS: ['dapps', 'web3', 'blockchain', 'defi', 'nft', 'crypto'],
  LOGO_URL: '/logo.png',
  FAVICON_URL: '/favicon.ico',
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  DEFAULT_THEME: 'system',
  THEMES: ['light', 'dark', 'system'] as const,
  STORAGE_KEY: 'theme-preference',
} as const;

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 8,
  MAX_LIMIT: 50,
  MIN_LIMIT: 1,
  ITEMS_PER_PAGE_OPTIONS: [8, 16, 24, 32, 48] as const,
} as const;

// File Upload Configuration
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] as const,
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'text/plain'] as const,
  MAX_FILES: 10,
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks
  FILENAME_SANITIZER: /[^a-zA-Z0-9]/g,
  FILENAME_REPLACEMENT: '_',
  DEFAULT_FILENAME: 'dapp',
  LOGO_EXTENSION: '.png',
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 254,
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBER: true,
    REQUIRE_SPECIAL_CHAR: false,
  },
  DAPP_NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
  },
  DAPP_DESCRIPTION: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 1000,
  },
  URL: {
    PATTERN: /^https?:\/\/.+/,
  },
} as const;

// Date and Time Configuration
export const DATE_TIME_CONFIG = {
  DEFAULT_LOCALE: 'en-US',
  DEFAULT_TIMEZONE: 'UTC',
  DATE_FORMAT: 'MMM dd, yyyy',
  TIME_FORMAT: 'HH:mm',
  DATETIME_FORMAT: 'MMM dd, yyyy HH:mm',
  RELATIVE_TIME_THRESHOLD: 7 * 24 * 60 * 60 * 1000, // 7 days
} as const;



// Cache Configuration
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  USER_DATA_TTL: 15 * 60 * 1000, // 15 minutes
  DAPPS_LIST_TTL: 10 * 60 * 1000, // 10 minutes
  STATIC_DATA_TTL: 60 * 60 * 1000, // 1 hour
  MAX_CACHE_SIZE: 100,
} as const;

// Animation Configuration
export const ANIMATION_CONFIG = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  EASING: {
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
  },
  SPRING: {
    STIFF: { tension: 300, friction: 30 },
    GENTLE: { tension: 120, friction: 14 },
    WOBBLY: { tension: 180, friction: 12 },
  },
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'bnry-theme',
  USER_PREFERENCES: 'bnry-user-preferences',
  DRAFT_DAPP: 'bnry-draft-dapp',
  ONBOARDING_COMPLETED: 'bnry-onboarding-completed',
  LAST_VISIT: 'bnry-last-visit',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_DARK_MODE: true,
  ENABLE_BETA_FEATURES: false,
  ENABLE_MAINTENANCE_MODE: false,
  ENABLE_RATE_LIMITING: true,
} as const;

// Social Media Links
export const SOCIAL_LINKS = {
  TWITTER: 'https://twitter.com/bnrydapps',
  GITHUB: 'https://github.com/bnrydapps',
  DISCORD: 'https://discord.gg/bnrydapps',
  TELEGRAM: 'https://t.me/bnrydapps',
  LINKEDIN: 'https://linkedin.com/company/bnrydapps',
  YOUTUBE: 'https://youtube.com/@bnrydapps',
} as const;

// Contact Information
export const CONTACT_INFO = {
  EMAIL: '<EMAIL>',
  SUPPORT_EMAIL: '<EMAIL>',
  BUSINESS_EMAIL: '<EMAIL>',
  PHONE: '+****************',
  ADDRESS: '123 Web3 Street, Blockchain City, BC 12345',
} as const;

// SEO Configuration
export const SEO_CONFIG = {
  DEFAULT_TITLE: 'BNRY dApps - Discover Web3 Applications',
  TITLE_TEMPLATE: '%s | BNRY dApps',
  DEFAULT_DESCRIPTION: 'Discover and explore the best decentralized applications on the blockchain',
  DEFAULT_KEYWORDS: ['dapps', 'web3', 'blockchain', 'defi', 'nft', 'crypto'],
  SITE_URL: 'https://bnrydapps.com',
  TWITTER_HANDLE: '@bnrydapps',
  FACEBOOK_APP_ID: '',
  GOOGLE_SITE_VERIFICATION: '',
} as const;

// Analytics Configuration
export const ANALYTICS_CONFIG = {
  GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GA_ID,
  MIXPANEL_TOKEN: process.env.NEXT_PUBLIC_MIXPANEL_TOKEN,
  HOTJAR_ID: process.env.NEXT_PUBLIC_HOTJAR_ID,
  ENABLE_TRACKING: process.env.NODE_ENV === 'production',
} as const;

// Error Tracking Configuration
export const ERROR_TRACKING_CONFIG = {
  SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  ENVIRONMENT: process.env.NODE_ENV,
  ENABLE_ERROR_TRACKING: process.env.NODE_ENV === 'production',
  SAMPLE_RATE: 1.0,
  TRACES_SAMPLE_RATE: 0.1,
} as const;
