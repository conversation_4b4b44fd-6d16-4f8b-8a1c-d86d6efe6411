/**
 * Main Constants Export
 * Re-exports all constants from different modules
 */

// Re-export all constants
export * from "./api";
export * from "./routes";
export * from "./messages";
export * from "./app";
export * from "./supabase";

// Legacy exports for backward compatibility
export interface Category {
  value: string;
  label: string;
}

export enum CategoryType {
  Trending = "trending",
  PopularDapps = "popular-dapps",
  TopDApps = "top-dapps",
  GameFi = "gamefi",
}

export const CATEGORIES: Category[] = [
  {
    value: "DeFi",
    label: "DeFi",
  },
  {
    value: "NFT",
    label: "NFT",
  },
  {
    value: "Games",
    label: "Games",
  },
  {
    value: "Tools",
    label: "Tools",
  },
  {
    value: "Social",
    label: "Social",
  },
  {
    value: "Multi-chain",
    label: "Multi-chain",
  },
  {
    value: "Wallet",
    label: "Wallet",
  },
  {
    value: "Security",
    label: "Security",
  },
];

// URL Configuration Constants
export const URL_CONFIG = {
  PRODUCTION_DOMAIN: "https://bnrydapps.com",
  DEVELOPMENT_DOMAINS: ["http://localhost:3000", "http://127.0.0.1:3000"],
  RESET_PASSWORD_PATH: "/auth/new-password",

  // Get appropriate domain based on environment
  getCurrentDomain: (host?: string) => {
    if (!host) return URL_CONFIG.PRODUCTION_DOMAIN;

    const isDev = host.includes("localhost") || host.includes("127.0.0.1");

    if (isDev) {
      // Return the exact localhost URL that matches the current host
      return URL_CONFIG.DEVELOPMENT_DOMAINS.find((domain) => domain.includes(host)) || `http://${host}`;
    }

    return URL_CONFIG.PRODUCTION_DOMAIN;
  },
} as const;

export const Env = {
  BUCKET_BASE_URL: process.env.NEXT_PUBLIC_BUCKET_BASE_URL,
  SUPABASE: {
    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  },
} as const;
