/**
 * Route Constants
 * Centralized route paths and navigation
 */

// Public Routes
export const PUBLIC_ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  CONTACT: '/contact',
  PRICING: '/pricing',
  TERMS: '/terms',
  PRIVACY: '/privacy',
} as const;

// Authentication Routes
export const AUTH_ROUTES = {
  SIGN_IN: '/auth/sign-in',
  SIGN_UP: '/auth/sign-up',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  NEW_PASSWORD: '/auth/new-password',
  VERIFY_EMAIL: '/auth/verify-email',
  OAUTH_CALLBACK: '/auth/callback',
} as const;

// Protected Routes
export const PROTECTED_ROUTES = {
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  UPLOAD: '/upload',
  CONGRATULATIONS: '/congratulations',
} as const;

// DApp Routes
export const DAPP_ROUTES = {
  LIST: '/dapps',
  DETAIL: (slug: string, id: string) => `/dapps/${slug}/${id}`,
  CATEGORY: (category: string) => `/dapps/category/${category}`,
} as const;

// Explore Routes
export const EXPLORE_ROUTES = {
  BASE: '/explore',
  CATEGORY: (category: string) => `/explore/${category}`,
  TRENDING: '/explore/trending',
  NEW: '/explore/new',
  TOP: '/explore/top',
} as const;

// Admin Routes
export const ADMIN_ROUTES = {
  DASHBOARD: '/admin',
  USERS: '/admin/users',
  DAPPS: '/admin/dapps',
  ANALYTICS: '/admin/analytics',
  SETTINGS: '/admin/settings',
} as const;

// API Routes (for internal navigation)
export const API_ROUTES = {
  DOCS: '/api/docs',
  HEALTH: '/api/health',
  STATUS: '/api/status',
} as const;

// External Routes
export const EXTERNAL_ROUTES = {
  GITHUB: 'https://github.com/bnrydapps',
  TWITTER: 'https://twitter.com/bnrydapps',
  DISCORD: 'https://discord.gg/bnrydapps',
  TELEGRAM: 'https://t.me/bnrydapps',
  DOCUMENTATION: 'https://docs.bnrydapps.com',
  SUPPORT: 'https://support.bnrydapps.com',
} as const;

// Route Groups for easier management
export const ROUTE_GROUPS = {
  PUBLIC: Object.values(PUBLIC_ROUTES),
  AUTH: Object.values(AUTH_ROUTES),
  PROTECTED: Object.values(PROTECTED_ROUTES),
  ADMIN: Object.values(ADMIN_ROUTES),
} as const;

// Route Metadata
export const ROUTE_META = {
  [PUBLIC_ROUTES.HOME]: {
    title: 'BNRY dApps - Discover Web3 Applications',
    description: 'Discover and explore the best decentralized applications on the blockchain',
    keywords: ['dapps', 'web3', 'blockchain', 'defi', 'nft'],
  },
  [AUTH_ROUTES.SIGN_IN]: {
    title: 'Sign In - BNRY dApps',
    description: 'Sign in to your BNRY dApps account',
    keywords: ['signin', 'login', 'authentication'],
  },
  [AUTH_ROUTES.SIGN_UP]: {
    title: 'Sign Up - BNRY dApps',
    description: 'Create your BNRY dApps account',
    keywords: ['signup', 'register', 'create account'],
  },
  [PROTECTED_ROUTES.DASHBOARD]: {
    title: 'Dashboard - BNRY dApps',
    description: 'Your personal dashboard',
    keywords: ['dashboard', 'profile', 'account'],
  },
} as const;

// Navigation Items
export const NAVIGATION_ITEMS = {
  MAIN: [
    { label: 'Home', href: PUBLIC_ROUTES.HOME },
    { label: 'Explore', href: EXPLORE_ROUTES.BASE },
    { label: 'Pricing', href: PUBLIC_ROUTES.PRICING },
    { label: 'About', href: PUBLIC_ROUTES.ABOUT },
  ],
  FOOTER: [
    { label: 'Terms', href: PUBLIC_ROUTES.TERMS },
    { label: 'Privacy', href: PUBLIC_ROUTES.PRIVACY },
    { label: 'Contact', href: PUBLIC_ROUTES.CONTACT },
    { label: 'Support', href: EXTERNAL_ROUTES.SUPPORT },
  ],
  USER_MENU: [
    { label: 'Dashboard', href: PROTECTED_ROUTES.DASHBOARD },
    { label: 'Profile', href: PROTECTED_ROUTES.PROFILE },
    { label: 'Settings', href: PROTECTED_ROUTES.SETTINGS },
  ],
} as const;

// Redirect Routes
export const REDIRECT_ROUTES = {
  AFTER_SIGN_IN: PUBLIC_ROUTES.HOME,
  AFTER_SIGN_UP: AUTH_ROUTES.VERIFY_EMAIL,
  AFTER_SIGN_OUT: AUTH_ROUTES.SIGN_IN,
  UNAUTHORIZED: AUTH_ROUTES.SIGN_IN,
  NOT_FOUND: PUBLIC_ROUTES.HOME,
  ERROR: PUBLIC_ROUTES.HOME,
} as const;
