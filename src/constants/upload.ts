// Upload page constants
export const UPLOAD_CONSTANTS = {
  // Payment methods
  PAYMENT_METHODS: {
    WALLET: "Wallet" as const,
    CARD: "Card" as const,
  },

  // Blockchain constants
  BLOCKCHAIN: {
    NETWORK: "OP",
    GAS_TOKEN: "ETH", 
    PAYMENT_TOKEN: "BNRY",
    ANNUAL_MULTIPLIER: 12,
  },

  // File upload limits
  FILE_LIMITS: {
    LOGO_MAX_SIZE: 2 * 1024 * 1024, // 2MB
    SCREENSHOT_MAX_SIZE: 5 * 1024 * 1024, // 5MB
    MAX_SCREENSHOTS: 5,
  },

  // Form validation
  VALIDATION: {
    DAPP_NAME_MIN: 3,
    DAPP_NAME_MAX: 100,
    DESCRIPTION_MIN: 3,
    DESCRIPTION_MAX: 1000,
    DECIMAL_PLACES: 5,
    MIN_PAYMENT_AMOUNT: 0.01,
  },

  // UI constants
  UI: {
    BUTTON_HEIGHT: "h-12",
    ICON_SIZE: "size-12",
    SKELETON_FIELDS: [1, 2, 3, 4],
  },

  // External links
  LINKS: {
    ENKRYPTED_EXTENSION: "https://chromewebstore.google.com/detail/enkrypted/kcecbbekclpmaclnbjhknnjepeoplkli",
    MEXC_EXCHANGE: "https://www.mexc.co/en-IN/exchange/BNRY_USDT",
    BINGX_EXCHANGE: "https://bingx.com/en/spot/BNRYUSDT",
  },

  // Messages
  MESSAGES: {
    WALLET_INSTALL: "Install the extension to pay with wallet,",
    WALLET_INSTALL_LINK: "Click here to install",
    WALLET_DISCONNECT_SUCCESS: "Wallet disconnected successfully",
    PAYMENT_FAILED: "Payment failed. Please try again.",
    DAPP_SUBMIT_SUCCESS: "DApp submitted successfully!",
    FREE_DAPP_BUTTON: "Create DApp for FREE!",
    REQUIRED_BALANCE: "Required balance:",
    BUY_BNRY_LINK: "Click here to buy $BNRY",
  },

  // Colors and styling
  COLORS: {
    GREEN_BORDER: "#22bc6b",
    TEXT_MUTED: "#A6A6A7",
  },

  // Exchange modal
  EXCHANGE: {
    MEXC: {
      NAME: "MEXC",
      COLOR: "bg-blue-600",
      SIZE: "w-20 h-12",
    },
    BINGX: {
      NAME: "BingX", 
      COLOR: "bg-orange-500",
      SIZE: "w-20 h-12",
    },
  },
} as const;

// Type exports
export type PaymentMethod = typeof UPLOAD_CONSTANTS.PAYMENT_METHODS[keyof typeof UPLOAD_CONSTANTS.PAYMENT_METHODS];
export type UploadStep = 1 | 2 | 3;
