/**
 * Message Constants
 * Centralized user-facing messages, errors, and notifications
 */

// Success Messages
export const SUCCESS_MESSAGES = {
  // Authentication
  SIGN_IN_SUCCESS: 'Welcome back!',
  SIGN_UP_SUCCESS: 'Account created successfully! Please check your email to verify your account.',
  SIGN_OUT_SUCCESS: 'Successfully signed out',
  PASSWORD_RESET_SENT: 'Password reset link sent to your email',
  PASSWORD_CHANGED: 'Password changed successfully',
  EMAIL_VERIFIED: 'Email verified successfully',
  
  // DApps
  DAPP_CREATED: 'DApp created successfully',
  DAPP_UPDATED: 'DApp updated successfully',
  DAPP_DELETED: 'DApp deleted successfully',
  DAPP_PUBLISHED: 'DApp published successfully',
  
  // General
  SAVE_SUCCESS: 'Changes saved successfully',
  UPLOAD_SUCCESS: 'File uploaded successfully',
  COPY_SUCCESS: 'Copied to clipboard',
  FORM_SUBMITTED: 'Form submitted successfully',
  
  // Payment
  PAYMENT_SUCCESS: 'Payment completed successfully',
  SUBSCRIPTION_CREATED: 'Subscription created successfully',
  SUBSCRIPTION_CANCELLED: 'Subscription cancelled successfully',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  USER_NOT_FOUND: 'User not found',
  USER_ALREADY_EXISTS: 'User already exists',
  EMAIL_NOT_VERIFIED: 'Please verify your email before signing in',
  SESSION_EXPIRED: 'Your session has expired. Please sign in again.',
  UNAUTHORIZED: 'You are not authorized to access this resource',
  FORBIDDEN: 'Access denied',
  
  // Validation
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PASSWORD: 'Password must be at least 8 characters long',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_URL: 'Please enter a valid URL',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size is too large',
  
  // Network
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  TIMEOUT_ERROR: 'Request timeout. Please try again.',
  CONNECTION_FAILED: 'Failed to connect to server',
  
  // DApps
  DAPP_NOT_FOUND: 'DApp not found',
  DAPP_CREATION_FAILED: 'Failed to create DApp',
  DAPP_UPDATE_FAILED: 'Failed to update DApp',
  DAPP_DELETE_FAILED: 'Failed to delete DApp',
  
  // Payment
  PAYMENT_FAILED: 'Payment failed. Please try again.',
  INVALID_PAYMENT_METHOD: 'Invalid payment method',
  SUBSCRIPTION_FAILED: 'Failed to create subscription',
  
  // General
  SOMETHING_WENT_WRONG: 'Something went wrong. Please try again.',
  FEATURE_NOT_AVAILABLE: 'This feature is not available yet',
  MAINTENANCE_MODE: 'The system is under maintenance. Please try again later.',
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later.',

  // Wallet
  INVALID_TRANSACTION: 'Invalid transaction. Please check the details and try again.',
} as const;

// Loading Messages
export const LOADING_MESSAGES = {
  // Authentication
  SIGNING_IN: 'Signing in...',
  SIGNING_UP: 'Creating account...',
  SIGNING_OUT: 'Signing out...',
  LOADING_USER: 'Loading user data...',
  VERIFYING_EMAIL: 'Verifying email...',
  RESETTING_PASSWORD: 'Resetting password...',
  
  // DApps
  LOADING_DAPPS: 'Loading DApps...',
  CREATING_DAPP: 'Creating DApp...',
  UPDATING_DAPP: 'Updating DApp...',
  DELETING_DAPP: 'Deleting DApp...',
  UPLOADING_FILE: 'Uploading file...',
  
  // Payment
  PROCESSING_PAYMENT: 'Processing payment...',
  CREATING_SUBSCRIPTION: 'Creating subscription...',
  
  // General
  LOADING: 'Loading...',
  SAVING: 'Saving...',
  SUBMITTING: 'Submitting...',
  PLEASE_WAIT: 'Please wait...',
} as const;

// Confirmation Messages
export const CONFIRMATION_MESSAGES = {
  DELETE_DAPP: 'Are you sure you want to delete this DApp? This action cannot be undone.',
  SIGN_OUT: 'Are you sure you want to sign out?',
  CANCEL_SUBSCRIPTION: 'Are you sure you want to cancel your subscription?',
  DELETE_ACCOUNT: 'Are you sure you want to delete your account? This action cannot be undone.',
  DISCARD_CHANGES: 'Are you sure you want to discard your changes?',
  LEAVE_PAGE: 'You have unsaved changes. Are you sure you want to leave?',
} as const;

// Info Messages
export const INFO_MESSAGES = {
  EMAIL_VERIFICATION_SENT: 'A verification email has been sent to your email address.',
  PASSWORD_REQUIREMENTS: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.',
  FILE_UPLOAD_REQUIREMENTS: 'Supported formats: JPG, PNG, GIF. Max size: 5MB.',
  MAINTENANCE_NOTICE: 'Scheduled maintenance will occur on [DATE] from [TIME] to [TIME].',
  BETA_FEATURE: 'This is a beta feature. Please report any issues.',
  COMING_SOON: 'This feature is coming soon!',
} as const;

// Placeholder Messages
export const PLACEHOLDER_MESSAGES = {
  SEARCH: 'Search DApps...',
  EMAIL: 'Enter your email',
  PASSWORD: 'Enter your password',
  DAPP_NAME: 'Enter DApp name',
  DAPP_DESCRIPTION: 'Describe your DApp',
  WEBSITE_URL: 'https://example.com',
  CATEGORY: 'Select a category',
  TAGS: 'Add tags (comma separated)',
} as const;

// Button Labels
export const BUTTON_LABELS = {
  // Actions
  SAVE: 'Save',
  CANCEL: 'Cancel',
  SUBMIT: 'Submit',
  CONTINUE: 'Continue',
  BACK: 'Back',
  NEXT: 'Next',
  FINISH: 'Finish',
  RETRY: 'Retry',
  REFRESH: 'Refresh',
  
  // Authentication
  SIGN_IN: 'Sign In',
  SIGN_UP: 'Sign Up',
  SIGN_OUT: 'Sign Out',
  FORGOT_PASSWORD: 'Forgot Password?',
  RESET_PASSWORD: 'Reset Password',
  
  // DApps
  CREATE_DAPP: 'Create DApp',
  EDIT_DAPP: 'Edit DApp',
  DELETE_DAPP: 'Delete DApp',
  PUBLISH_DAPP: 'Publish DApp',
  VIEW_DAPP: 'View DApp',
  
  // General
  LEARN_MORE: 'Learn More',
  GET_STARTED: 'Get Started',
  CONTACT_US: 'Contact Us',
  DOWNLOAD: 'Download',
  UPLOAD: 'Upload',
  COPY: 'Copy',
  SHARE: 'Share',
} as const;

// Status Messages
export const STATUS_MESSAGES = {
  ONLINE: 'Online',
  OFFLINE: 'Offline',
  CONNECTING: 'Connecting...',
  CONNECTED: 'Connected',
  DISCONNECTED: 'Disconnected',
  SYNCING: 'Syncing...',
  SYNCED: 'Synced',
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  DRAFT: 'Draft',
  PUBLISHED: 'Published',
} as const;

// Form Interaction Constants
export const FORM_INTERACTION = {
  DISABLED_DURING_LOADING: true,
  ENABLED: false,
} as const;

// Form loading states for consistent UX
export const FORM_LOADING_STATES = {
  // Authentication forms
  SIGN_IN_LOADING: 'signing-in',
  SIGN_UP_LOADING: 'signing-up', 
  RESET_PASSWORD_LOADING: 'resetting-password',
  NEW_PASSWORD_LOADING: 'updating-password',
  
  // App forms
  UPLOAD_DAPP_LOADING: 'uploading-dapp',
  PAYMENT_LOADING: 'processing-payment',
  
  // Generic
  FORM_SUBMITTING: 'form-submitting',
} as const;

// Form styling utilities for disabled states
export const FORM_STYLES = {
  // Input field disabled styles
  DISABLED_INPUT: 'disabled:opacity-50 disabled:cursor-not-allowed',
  
  // Button disabled styles
  DISABLED_BUTTON: 'disabled:opacity-50 disabled:cursor-not-allowed',
  
  // Link disabled styles
  DISABLED_LINK: 'pointer-events-none opacity-50',
  
  // Interactive element disabled styles
  DISABLED_INTERACTIVE: 'cursor-not-allowed opacity-50',
  
  // Generic disabled element styles
  DISABLED_ELEMENT: 'opacity-50',
  
  // Loading state combinations
  LOADING_STATE: {
    input: 'disabled:opacity-50 disabled:cursor-not-allowed',
    button: 'disabled:opacity-50 disabled:cursor-not-allowed',
    link: 'pointer-events-none opacity-50',
    icon: 'cursor-not-allowed opacity-50',
    element: 'opacity-50',
  },
} as const;

// Form utility functions
export const getFormFieldClass = (loading: boolean, baseClass: string = '') => {
  return `${baseClass} ${loading ? FORM_STYLES.DISABLED_INPUT : ''}`.trim();
};

export const getLinkClass = (loading: boolean, baseClass: string = '') => {
  return `${baseClass} ${loading ? FORM_STYLES.DISABLED_LINK : ''}`.trim();
};

export const getInteractiveClass = (loading: boolean, baseClass: string = '') => {
  return `${baseClass} ${loading ? FORM_STYLES.DISABLED_INTERACTIVE : ''}`.trim();
};
