"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, Check, X, Tag } from "lucide-react"
import { cn } from "@/lib/utils"
import { useCouponsApi } from "@/hooks/useApi"

interface CouponInputProps {
  onCouponApplied?: (couponData: {
    code: string
    isFree: boolean
  }) => void
  onCouponRemoved?: () => void
  className?: string
}

export function CouponInput({
  onCouponApplied,
  onCouponRemoved,
  className
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState("")
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null)
  const [error, setError] = useState("")

  const couponsApi = useCouponsApi()

  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setError("Please enter a coupon code")
      return
    }

    setError("")
    const result = await couponsApi.execute(couponCode.trim())

    if (result.success && result.data) {
      setAppliedCoupon(result.data)
      if (result.data.coupon) {
        onCouponApplied?.(result.data.coupon)
      }
      setError("")
    } else {
      setError(result.error || 'Failed to validate coupon')
      setAppliedCoupon(null)
    }
  }

  const removeCoupon = () => {
    setCouponCode("")
    setAppliedCoupon(null)
    setError("")
    onCouponRemoved?.()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      validateCoupon()
    }
  }

  return (
    <div className={cn("space-y-3", className)}>
      <Label htmlFor="coupon" className="text-sm font-medium">
        Coupon Code (FREE DApp listing!)
      </Label>
      
      {!appliedCoupon ? (
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              id="coupon"
              placeholder="Enter coupon code"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
              onKeyDown={handleKeyPress}
              className="pl-10"
              disabled={couponsApi.loading}
            />
          </div>
          <Button
            onClick={validateCoupon}
            disabled={couponsApi.loading || !couponCode.trim()}
            size="default"
          >
            {couponsApi.loading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              "Apply"
            )}
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Check className="w-4 h-4 text-green-600" />
            <div>
              <p className="text-sm font-medium text-green-800">
                🎉 Coupon Applied: {appliedCoupon.coupon.code}
              </p>
              <p className="text-xs text-green-600">
                DApp listing is now FREE!
              </p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={removeCoupon}
            className="text-green-700 hover:text-green-800"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}