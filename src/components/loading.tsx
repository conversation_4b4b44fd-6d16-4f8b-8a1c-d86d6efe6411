const messages = [
  "Unlocking",
  "Please wait",
  "Processing",
  "Almost there",
  "Verifying data",
  "Sealing Vault",
  "Deriving Secrets",
  "Fetching Keys",
  "Decoding Payload",
  "Engaging Cipher",
  "Securing Channel",
  "Decrypt Tunnel",
];

const randomMsg = () => {
  return messages[Math.floor(Math.random() * messages.length)];
};

const Loader = ({ msg = "" }) => (
  <div className="fixed left-0 top-0 z-[9998] size-full bg-white/[.85] dark:bg-black/90 rounded-lg">
    <div className="flex h-full items-center justify-center">
      <div className="flex flex-col items-center justify-center gap-6">
        <div className="relative inline-block w-10 h-10 rounded-full border-[5px] border-[#334ee680] box-border animate-spin">
          <div className="absolute left-1/2 top-1/2 w-12 h-12 -translate-x-1/2 -translate-y-1/2 rounded-full border-[5px] border-transparent border-b-[#334ee6] box-border"></div>
        </div>
        <div className="text-blue-bold font-medium text-lg shadow text-center">
          {msg || randomMsg()}
        </div>
      </div>
    </div>
  </div>
);



export default Loader;
