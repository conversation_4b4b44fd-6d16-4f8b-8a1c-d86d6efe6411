"use client";
import Image from "next/image";
import { motion } from "motion/react";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const Discovery = () => {
  return (
    <div>
      <div className="container lg:my-24 my-4">
        <motion.h2
          whileInView={{
            opacity: [0, 100],
            y: [-50, 0],
            transition: {
              duration: 0.5,
            },
          }}
          viewport={{ once: true }}
          className="text-center font-protest-strike text-xl lg:text-[28px] text-primary uppercase"
        >
          BNRY DAPPS DNA: What Powers <br className="md:block" /> the Platform
        </motion.h2>
        <motion.p
          whileInView={{
            opacity: [0, 100],
            y: [-50, 0],
            transition: {
              duration: 0.5,
              delay: 0.2,
            },
          }}
          viewport={{ once: true }}
          className="text-center mx-auto mt-2 text-secondary-foreground"
        >
          Essential pillars fueling discovery, innovation, and global scale for
          the <br className="hidden lg:block" /> next generation of
          decentralized applications.
        </motion.p>

        <div className="hidden lg:grid grid-cols-1 lg:grid-cols-2 gap-5 mt-8 lg:mt-14">
          <motion.div
            whileInView={{
              opacity: [0, 100],
              x: [30, 0],
              transition: {
                duration: 0.5,
                delay: 0.4,
              },
            }}
            viewport={{ once: true }}
            className="col-span-2 lg:col-span-1 p-6 rounded-2xl bg-card grid grid-cols-3"
          >
            <div className="row-start-2 lg:row-start-1 col-span-3 lg:col-span-2 flex flex-col gap-3">
              <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                Open Access for All Creators
              </p>

              <p className="text-secondary-foreground text-sm lg:text-base">
                Decentralization with no boundaries. Whether you&apos;re
                building DeFi tools, NFT marketplaces, social communities,
                educational apps, or entertainment DApps — BNRY DAPPS is your
                launchpad.
              </p>
            </div>
            <div className="col-span-3 lg:col-span-1 flex items-center justify-center">
              <div className="size-[180px] md:size-full relative">
                <Image
                  src={
                    "/405233855_877cdd99-3a20-4212-a02c-1eb89ff6616b copy 1.svg"
                  }
                  sizes="auto"
                  className="object-contain"
                  alt=""
                  fill
                />
              </div>
            </div>
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              x: [30, 0],
              transition: {
                duration: 0.5,
                delay: 0.6,
              },
            }}
            viewport={{ once: true }}
            className="col-span-2 lg:col-span-1 p-6 rounded-2xl bg-card grid grid-cols-3"
          >
            <div className="lg:row-start-1 row-start-2 col-span-3 lg:col-span-2 flex flex-col gap-3">
              <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                Smart Distribution via One Wave
              </p>
              <p className="text-secondary-foreground text-sm lg:text-base">
                Upload once, distribute everywhere. Once published on BNRY
                DAPPS, your DApp is instantly available to One Wave&apos;s vast
                global community.
              </p>
            </div>
            <div className="col-span-3 lg:col-span-1 flex items-center justify-center">
              <div className="size-[184px] lg:size-full relative">
                <Image
                  src={
                    "/freepik__the-style-is-3d-model-with-octane-render-volumetri__22662.svg"
                  }
                  sizes="auto"
                  className="object-contain"
                  alt=""
                  fill
                />
              </div>
            </div>
          </motion.div>

          <motion.div
            whileInView={{
              opacity: [0, 100],
              x: [30, 0],
              transition: {
                duration: 0.5,
                delay: 1,
              },
            }}
            viewport={{ once: true }}
            className="col-span-2 p-6 rounded-2xl bg-card flex flex-col-reverse lg:flex-row justify-between items-center"
          >
            <div className="flex flex-col gap-3 max-w-3xl">
              <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                Powering the BNRY DAPPS Ecosystem
              </p>
              <p className="text-secondary-foreground text-sm lg:text-base">
                A living, breathing decentralized network. <br />
                BNRY DAPPS isn&apos;t just a listing platform — it’s a
                full-stack ecosystem designed to foster innovation, creator
                success, and decentralized community growth.
              </p>
            </div>
            <div className="flex items-center justify-center">
              <div className="size-60 relative">
                <Image
                  src={
                    "/3d-flat-icon-digital-rainforest-concept-lush-forest-with-glowing-digital-vines-circuit-pattern 1.svg"
                  }
                  sizes="auto"
                  className="object-contain"
                  alt=""
                  fill
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container lg:my-24 my-4 lg:hidden">
        <Swiper {...swiperConfig}>
          <SwiperSlide className="h-full">
            <div className="col-span-2 lg:col-span-1 p-6 rounded-2xl bg-card grid grid-cols-3">
              <div className="row-start-2 lg:row-start-1 col-span-3 lg:col-span-2 flex flex-col gap-3">
                <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                  Open Access for All Creators
                </p>

                <p className="text-secondary-foreground text-sm lg:text-base">
                  Decentralization with no boundaries. Whether you&apos;re
                  building DeFi tools, NFT marketplaces, social communities,
                  educational apps, or entertainment DApps — BNRY DAPPS is your
                  launchpad.
                </p>
              </div>
              <div className="col-span-3 lg:col-span-1 flex items-center justify-center">
                <div className="size-[180px] md:size-full relative">
                  <Image
                    src={
                      "/405233855_877cdd99-3a20-4212-a02c-1eb89ff6616b copy 1.svg"
                    }
                    sizes="auto"
                    className="object-contain"
                    alt=""
                    fill
                  />
                </div>
              </div>
            </div>
          </SwiperSlide>

          <SwiperSlide className="h-full">
            <div className="col-span-2 lg:col-span-1 p-6 rounded-2xl bg-card grid grid-cols-3">
              <div className="lg:row-start-1 row-start-2 col-span-3 lg:col-span-2 flex flex-col gap-3">
                <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                  Smart Distribution via One Wave
                </p>
                <p className="text-secondary-foreground text-sm lg:text-base">
                  Upload once, distribute everywhere. Once published on BNRY
                  DAPPS, your DApp is instantly available to One Wave&apos;s
                  vast global community.
                </p>
              </div>
              <div className="col-span-3 lg:col-span-1 flex items-center justify-center">
                <div className="size-[184px] lg:size-full relative">
                  <Image
                    src={
                      "/freepik__the-style-is-3d-model-with-octane-render-volumetri__22662.svg"
                    }
                    sizes="auto"
                    className="object-contain"
                    alt=""
                    fill
                  />
                </div>
              </div>
            </div>
          </SwiperSlide>

          <SwiperSlide className="h-full">
            <div className="col-span-2 p-6 rounded-2xl bg-card flex flex-col-reverse lg:flex-row justify-between items-center">
              <div className="flex flex-col gap-3 max-w-3xl">
                <p className="font-protest-strike text-xl lg:text-3xl text-primary uppercase">
                  Powering the BNRY DAPPS Ecosystem
                </p>
                <p className="text-secondary-foreground text-sm lg:text-base">
                  A living, breathing decentralized network. <br />
                  BNRY DAPPS isn&apos;t just a listing platform — it’s a
                  full-stack ecosystem designed to foster innovation, creator
                  success, and decentralized community growth.
                </p>
              </div>
              <div className="flex items-center justify-center">
                <div className="size-[184px] lg:size-full relative">
                  <Image
                    src={
                      "/3d-flat-icon-digital-rainforest-concept-lush-forest-with-glowing-digital-vines-circuit-pattern 1.svg"
                    }
                    sizes="auto"
                    className="object-contain"
                    alt=""
                    fill
                  />
                </div>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  );
};

export default Discovery;
