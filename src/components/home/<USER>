"use client";
import Image from "next/image";
import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { motion } from "motion/react";
import { PROTECTED_ROUTES } from "@/constants/routes";

// Constants for easy customization
const UPLOAD_ROUTE = PROTECTED_ROUTES.UPLOAD;

const GuideLine = () => {
  const router = useRouter();

  const handleSubmitDapps = () => {
    router.push(UPLOAD_ROUTE);
  };

  return (
    <div className="container lg:my-24 gap-y-10 my-4 p-8 lg:p-12 bg-card border rounded-2xl grid lg:grid-cols-2 relative z-10">
      <div className="absolute top-0 left-0 size-full">
        <Image
          src={"/dark_submit.svg"}
          alt=""
          sizes="auto"
          fill
          className="object-cover"
        />
      </div>

      <div className="relative z-50">
        <div className="lg:max-w-[360px]">
          <motion.div
            whileInView={{
              x: [-40, 0],
              opacity: [0, 1],
              transition: { duration: 0.5 },
            }}
            viewport={{ once: true }}
            className="size-28 relative"
          >
            <Image
              sizes="auto"
              fill
              quality={100}
              alt=""
              src={
                "/freepik__the-style-is-3d-model-with-octane-render-volumetri__22662.svg"
              }
            />
          </motion.div>

          <motion.p
            whileInView={{
              x: [-60, 0],
              opacity: [0, 1],
              transition: { duration: 0.5, delay: 0.4 },
            }}
            viewport={{ once: true }}
            className="my-3 font-protest-strike text-xl lg:text-3xl uppercase"
          >
            Launch. Scale. Track. Your DApp journey starts here.
          </motion.p>

          <motion.p
            whileInView={{
              x: [-60, 0],
              opacity: [0, 1],
              transition: { duration: 0.5, delay: 0.6 },
            }}
            viewport={{ once: true }}
            className="text-secondary-foreground text-sm lg:text-base"
          >
            Effortlessly bring your decentralized app to life and reach millions
            of users with our easy 3-step process. From sign-up to real-time
            analytics, we&apos;ve got you covered.
          </motion.p>
        </div>

        <motion.div
          whileInView={{
            x: [-60, 0],
            opacity: [0, 1],
            transition: { duration: 1, delay: 1 },
          }}
          viewport={{ once: true }}
        >
          <Button 
            className="px-6 h-10 bg-green text-white mt-10 dark:hover:bg-secondary"
            onClick={handleSubmitDapps}
          >
            Submit your Dapps
          </Button>
        </motion.div>
      </div>

      <div className="flex flex-col justify-center">
        <motion.div
          whileInView={{
            y: [100, 0],
            opacity: [0, 1],
            transition: {
              duration: 0.5,
              delay: 0.6,
            },
          }}
          viewport={{ once: true }}
          className="flex gap-6 after:h-full after:w-0.5 after:bg-primary/10 relative after:absolute after:left-4.5 top-0 pb-11"
        >
          <div className="size-10 min-w-10 min-h-10 rounded-xl border bg-background flex items-center justify-center text-xl text-secondary-foreground font-protest-strike relative z-10">
            <p>1</p>
          </div>
          <div className="flex flex-col gap-2">
            <p className="font-protest-strike text-primary">
              Sign Up & Choose a Plan
            </p>
            <p className="text-sm text-secondary-foreground">
              Create your account in seconds using your email and select from
              three tailored subscription plans that best suit your business
              needs. Each plan is valid for one year and designed to help you
              scale.
            </p>
          </div>
        </motion.div>

        <motion.div
          whileInView={{
            y: [100, 0],
            opacity: [0, 1],
            transition: {
              duration: 0.5,
              delay: 0.8,
            },
          }}
          viewport={{ once: true }}
          className="flex gap-6 after:h-full after:w-0.5 after:bg-primary/10 relative after:absolute after:left-4.5 top-0 pb-11"
        >
          <div className="size-10 min-w-10 min-h-10 rounded-xl border bg-background flex items-center justify-center text-xl text-secondary-foreground font-protest-strike relative z-10">
            <p>2</p>
          </div>
          <div className="flex flex-col gap-2">
            <p className="font-protest-strike text-primary">
              Make Your Payment
            </p>
            <p className="text-sm text-secondary-foreground">
              Securely pay via Credit Card or Crypto. You can also connect
              MetaMask and pay using BNRY tokens for a seamless transaction
              experience.
            </p>
          </div>
        </motion.div>

        <motion.div
          whileInView={{
            y: [100, 0],
            opacity: [0, 1],
            transition: {
              duration: 0.5,
              delay: 1,
            },
          }}
          viewport={{ once: true }}
          className="flex gap-6"
        >
          <div className="size-10 min-w-10 min-h-10 rounded-xl border bg-background flex items-center justify-center text-xl text-secondary-foreground font-protest-strike relative z-10">
            <p>3</p>
          </div>
          <div className="flex flex-col gap-2">
            <p className="font-protest-strike text-primary">
              Get Listed & Track Success
            </p>
            <p className="text-sm text-secondary-foreground">
              Once your payment is complete, your DApp will be instantly listed
              on the BNRY portal and published on the One Wave App, reaching
              over 169.5 million users worldwide. Monitor real-time analytics
              from your personalized dashboard to track usage and optimize
              performance.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default GuideLine;
