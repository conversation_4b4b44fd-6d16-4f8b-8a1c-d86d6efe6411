"use client";
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>elegramPlane } from "react-icons/fa";
import { Separator } from "@/components/ui/separator";
import { FaXTwitter } from "react-icons/fa6";
import { useTheme } from "next-themes";
import Image from "next/image";
import React from "react";
import Link from "next/link";

const Footer = () => {
  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => {
    setMounted(true);
  }, []);

  const { theme } = useTheme();

  return (
    mounted && (
      <div className="bg-card pt-[30px] text-sm lg:text-base">
        <div className="flex gap-y-6 justify-between container">
          <div className="flex flex-col gap-5">
            <div className="flex gap-3 items-center">
              <div className="relative h-14 w-10">
                {!theme ? (
                  <Image src={"/tbh_logo.svg"} alt="" sizes="auto" fill />
                ) : (
                  <Image src={"/tbh_logo_dark.png"} alt="" sizes="auto" fill />
                )}
              </div>
              <p className="font-bold text-xl">
                BNRY <br />
                Dapps
              </p>
            </div>
            <div className="flex flex-col gap-1 text-secondary-foreground">
              <p className="text-secondary-foreground">Questions? Reach out to us!</p>
              <a className="underline cursor-pointer text-secondary-foreground" href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            </div>
          </div>

          <div className="w-fit flex flex-col gap-2 lg:gap-4 text-secondary-foreground justify-end">
            <p className="font-protest-strike text-foreground text-2xl">LEGAL</p>
            <Link href={"/terms"}>
              <p className="hover:underline cursor-pointer text-secondary-foreground">Terms of service</p>
            </Link>
            <Link href={"/privacy"}>
              <p className="hover:underline cursor-pointer text-secondary-foreground">Privacy policy</p>
            </Link>
          </div>
        </div>

        <Separator className="my-2 md:my-5" />

        <div className="container flex py-2 gap-y-3 justify-between text-secondary-foreground">
          <p className="text-secondary-foreground text-[10px] md:text-base flex justify-center items-center">
            © 2024 Binary Dapps. All rights reserved.
          </p>

          <div className="flex items-center gap-2 md:gap-4">
            <a
              href="https://t.me/tbhofficialchat"
              className="flex size-8 lg:size-10 relative items-center justify-center"
            >
              <FaTelegramPlane size={20} />
            </a>
            <a
              href="https://discord.gg/wCXJmTBGr2"
              className="flex size-8 lg:size-10 relative items-center justify-center"
            >
              <FaDiscord size={20} />
            </a>
            <a
              href="https://twitter.com/thebinaryhldgs"
              className="flex size-8 lg:size-10 relative items-center justify-center"
            >
              <FaXTwitter size={20} />
            </a>
          </div>
        </div>
      </div>
    )
  );
};

export default Footer;
