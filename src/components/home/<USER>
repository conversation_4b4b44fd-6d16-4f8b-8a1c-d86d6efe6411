"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { Lens } from "@/components/ui/lens";

interface HoverImageCardProps {
  imageLight: string;
  imageDark: string;
  title: string;
  description: string;
  isDark: boolean;
}

export const HoverImageCard: React.FC<HoverImageCardProps> = ({
  imageLight,
  imageDark,
  title,
  description,
  isDark,
}) => {
  const [hovering, setHovering] = useState(false);

  return (
    <div className="my-2 px-6 py-7 bg-card rounded-2xl border flex flex-col gap-4 h-full pointer-events-none">
      <div className="w-full h-64 relative">
        <Image
          quality={100}
          src={isDark ? imageDark : imageLight}
          alt={title}
          sizes="auto"
          fill
          className="object-contain object-center"
        />
      </div>

      <motion.div className="flex flex-col gap-2">
        <p className="font-protest-strike">{title}</p>
        <p className="text-sm text-secondary-foreground">{description}</p>
      </motion.div>
    </div>
  );
};
