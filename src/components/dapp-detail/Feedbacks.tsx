"use client";
import React, { useEffect, useState, useCallback } from "react";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "next-themes";
import { useParams } from "next/navigation";


const Feedbacks = () => {
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  // Helper function để tính phần trăm cho rating breakdown
  const getPercentage = (count: number, total: number) => {
    return total > 0 ? Math.round((count / total) * 100) : 0;
  };

  const param = useParams();
  const [avgSessionTime, setAvgSessionTime] = useState(0);
  const [avgRating, setAvgRating] = useState(0);
  const [totalReviews, setTotalReviews] = useState(0);
  const [session, setSession] = useState(0);
  const [playCount, setPlayCount] = useState(0);
  const [rating, setRating] = useState<any[]>([]);
  const [loading, setLoading] = useState("Loading...");
  const [ratingBreakdown, setRatingBreakdown] = useState<{[key: number]: number}>({
    5: 0, 4: 0, 3: 0, 2: 0, 1: 0
  });
  const [peakHours, setPeakHours] = useState<
    Array<{
      hour_of_day: number;
      hour_interval: string;
      users_count: number;
    }>
  >([]);

  const processPeakHours = (d2: any[]) => {
    const data = [];
    for (let i = 1; i <= 24; i++) {
      const existing = d2.find((dx) => dx.hour_of_day === i);
      const hour_of_day = existing?.hour_of_day || i;
      const hour_interval = `${i}:00 - ${i + 1}:00`;
      const users_count = existing?.users_count || 0; // Random number between 0 and 99
      data.push({ hour_of_day, hour_interval, users_count });
    }
    return data;
  };

  const getSession = useCallback(async (id: string) => {
    if (session) return;
    return fetch(`/api/v1/external/session?dappId=${id}`)
      .then((res) => res.json())
      .then((data) => {
        const pk = processPeakHours(data?.peak_hours);
        setPlayCount(data?.total_count || 0);
        setSession(data?.total_session_time || 0);
        setAvgSessionTime(data?.avg_session_time_today || 0);
        setPeakHours(pk || []);
      })
      .catch((err) => console.log(err));
  }, [session]);

  const getRatings = useCallback(async (id: string) => {
    return fetch(`/api/v1/external/ratings?dappId=${id}`)
      .then((res) => res.json())
      .then((data) => {
        const ratingsData = data || [];
        setRating(ratingsData);

        // Tính average rating: cộng tất cả rating rồi chia đều
        if (ratingsData.length > 0) {
          const totalRating = ratingsData.reduce((acc: number, curr: any) => acc + curr.rating, 0);
          const avg = totalRating / ratingsData.length;
          setAvgRating(avg);

          // Tính rating breakdown
          const breakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
          ratingsData.forEach((review: any) => {
            const rating = Math.floor(review.rating);
            if (rating >= 1 && rating <= 5) {
              breakdown[rating as keyof typeof breakdown]++;
            }
          });
          setRatingBreakdown(breakdown);
        } else {
          setAvgRating(0);
          setRatingBreakdown({ 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 });
        }

        setTotalReviews(ratingsData.length);
      })
      .catch((err) => {
        console.log(err);
        setRating([]);
        setAvgRating(0);
        setTotalReviews(0);
        setRatingBreakdown({ 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 });
      });
  }, []);

  useEffect(() => {
    if (param?.id && getSession && getRatings) {
      const dappId = param.id;
      const promises = [
        getSession(dappId.toString()),
        getRatings(dappId.toString()),
      ];
      Promise.all(promises).then(() => setLoading(""));
    }
  }, [param?.id, getSession, getRatings]);

  return (
    <div className="mx-auto max-w-[1360px] mt-10 grid lg:grid-cols-2 my-24">
      <div className="flex flex-col gap-12">
        <h2 className="uppercase font-protest-strike text-xl lg:text-3xl">
          Review & Feedback Panel
        </h2>

        <div className="flex flex-col lg:flex-row gap-14 lg:items-center">
          <div className="flex flex-col gap-[6px]">
            <p className="text-sm text-secondary-foreground">Average rating</p>
            <p className="text-[52px] font-bold">{avgRating.toFixed(1)}</p>
            <div className="flex gap-2 items-center">
              {Array.from({ length: 5 }).map((_, index) => (
                <svg
                  key={index}
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill={index < Math.floor(avgRating) ? "#FBAB29" : (isDarkMode ? "white" : "#cbcbcb")}
                    fillRule="evenodd"
                    d="m13.059.716 2.824 6.467q.045.105.133.173a.4.4 0 0 0 .198.078l6.751.813c.222.025.433.117.606.265.174.148.303.344.372.567.07.222.075.461.018.687s-.176.43-.341.588l-5.005 4.808a.437.437 0 0 0-.127.408l1.347 6.969c.046.229.027.466-.053.685-.08.218-.22.407-.4.544s-.396.218-.62.23a1.1 1.1 0 0 1-.639-.159l-5.918-3.494a.39.39 0 0 0-.41 0l-5.92 3.494a1.12 1.12 0 0 1-1.256-.071 1.2 1.2 0 0 1-.4-.545c-.081-.218-.1-.455-.055-.684l1.348-6.969a.44.44 0 0 0-.128-.408L.381 10.353a1.23 1.23 0 0 1-.342-.587 1.27 1.27 0 0 1 .018-.688c.07-.222.198-.42.372-.567.173-.148.384-.24.607-.265l6.751-.813a.4.4 0 0 0 .199-.078.4.4 0 0 0 .132-.173L10.942.716a1.2 1.2 0 0 1 .428-.521 1.12 1.12 0 0 1 1.26 0 1.2 1.2 0 0 1 .429.521"
                    clipRule="evenodd"
                  ></path>
                </svg>
              ))}
            </div>
          </div>
          <Separator orientation="vertical" className="hidden lg:block" />
          <div className="flex flex-col gap-3">
            {[5, 4, 3, 2, 1].map((starCount) => {
              const count = ratingBreakdown[starCount] || 0;
              const percentage = getPercentage(count, totalReviews);
              const colors = {
                5: '#51AC54',
                4: '#A6D032',
                3: '#F7EB44',
                2: '#FBA82C',
                1: '#F03E1D'
              };

              return (
                <div key={starCount} className="flex items-center gap-8">
                  <div className="flex items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      fill="none"
                      viewBox="0 0 12 12"
                    >
                      <path
                        fill="#FBAB29"
                        fillRule="evenodd"
                        d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                    <p className="text-sm text-secondary-foreground">{starCount}</p>
                  </div>

                  <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                    <div
                      className="h-full transition-all duration-300"
                      style={{
                        width: `${percentage}%`,
                        backgroundColor: colors[starCount as keyof typeof colors]
                      }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {rating.length > 0 ? (
          rating.map((review: any, index: number) => (
            <div key={index} className="flex flex-col gap-4">
              <div className="flex gap-3 items-center">
                <div className="size-10 min-w-10 min-h-10 rounded-md bg-primary/10 flex items-center justify-center text-[22px] text-secondary-foreground/70">
                  {review.user?.username?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div>
                  <p className="text-sm text-primary">{review.user?.username || 'Anonymous'}</p>
                  <div className="flex gap-1">
                    {Array.from({ length: 5 }).map((_, starIndex) => (
                      <svg
                        key={starIndex}
                        xmlns="http://www.w3.org/2000/svg"
                        width="12"
                        height="12"
                        fill="none"
                        viewBox="0 0 12 12"
                      >
                        <path
                          fill={starIndex < review.rating ? "#FBAB29" : (isDarkMode ? "white" : "#cbcbcb")}
                          fillRule="evenodd"
                          d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-sm text-secondary-foreground">
                {review.content || 'No review content provided.'}
              </p>
            </div>
          ))
        ) : (
          <div className="flex flex-col gap-4">
            <p className="text-sm text-secondary-foreground text-center">
              No reviews available yet.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Feedbacks;
