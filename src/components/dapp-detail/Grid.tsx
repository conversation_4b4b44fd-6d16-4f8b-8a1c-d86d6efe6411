import React, { useEffect, useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DApp } from "../home/<USER>";
import { getSafeImageSrc } from "@/lib/utils/image";

// Helper function to format dates
const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit'
    });
  } catch {
    return 'N/A';
  }
};

interface GridProps {
  dapp: DApp;
}

const Grid = ({ dapp }: GridProps) => {
  return (
    <div className="mx-auto max-w-[1360px]">
      <Breadcrumb className="">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{dapp.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mt-6 grid grid-cols-2 lg:grid-cols-6 grid-rows-2 gap-y-5 gap-x-4 lg:gap-x-5 ">
        <div className="col-span-2 lg:col-span-3 row-span-2 flex flex-col lg:flex-row gap-8 border rounded-2xl bg-card p-6">
          <div className="lg:min-h-56 lg:min-w-56 size-40 lg:size-56 rounded-2xl overflow-hidden relative">
            <Image src={getSafeImageSrc(dapp.logo) || '/dapps/image.svg'} alt="" sizes="auto" fill className="object-cover object-center" />
          </div>

          <div className="flex flex-col gap-3 flex-1">
            <div className="flex items-center justify-between w-full">
              <Badge className="bg-[#6745C14D] rounded-xl text-secondary-foreground px-3 text-xs py-2">
                {dapp.category}
              </Badge>

              <p className="text-secondary-foreground">
                Status: <span className="text-primary font-semibold">Active</span>
              </p>
            </div>

            <div className="flex flex-col gap-1">
              <p className="uppercase font-protest-strike text-3xl text-primary">{dapp.name}</p>
              <p className="line-clamp-3 text-secondary-foreground">{dapp.description}</p>
            </div>

            <Separator />

            <div className="grid grid-cols-2 text-secondary-foreground">
              <div className="flex flex-col">
                <p>Rating</p>
                <p className="font-medium text-primary">N/A</p>
              </div>
              <div className="flex flex-col">
                <p>Total views</p>
                <p className="font-medium text-primary">{dapp.total_views || 0}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile: Show all 3 cards separately */}
        <div className="col-span-1 lg:hidden p-6 rounded-2xl bg-card border flex items-center">
          <div className="flex flex-col w-full text-secondary-foreground">
            <p>Listed on</p>
            <p className="font-medium text-primary">{formatDate(dapp.created_at)}</p>
          </div>
        </div>
        <div className="col-span-1 lg:hidden p-6 rounded-2xl bg-card border flex items-center">
          <div className="flex flex-col w-full text-secondary-foreground">
            <p>Last update</p>
            <p className="font-medium text-primary">{formatDate(dapp.created_at)}</p>
          </div>
        </div>

        {/* Desktop: Show Listed on + Last update together */}
        <div className="hidden lg:flex lg:col-span-2 p-6 rounded-2xl bg-card border items-center">
          <div className="grid grid-cols-2 w-full text-secondary-foreground">
            <div className="flex flex-col">
              <p>Listed on</p>
              <p className="font-medium text-primary">{formatDate(dapp.created_at)}</p>
            </div>
            <div className="flex flex-col">
              <p>Last update</p>
              <p className="font-medium text-primary">{formatDate(dapp.created_at)}</p>
            </div>
          </div>
        </div>

        <div className="lg:col-span-1 p-6 rounded-2xl bg-card border-[2px] border-[#FA4343] flex items-center">
          <div className="flex flex-col">
            <p className="text-secondary-foreground">Expires on</p>
            <p className="font-medium text-primary">{(dapp as any).validtill ? formatDate((dapp as any).validtill) : 'N/A'}</p>
          </div>
        </div>
        <div className="col-span-2 lg:col-span-3 p-6 rounded-2xl bg-card border text-secondary-foreground flex items-center">
          <div className="grid grid-cols-3 w-full">
            <div className="flex flex-col">
              <p className="text-sm">Users online</p>
              <p className="font-medium text-primary">N/A</p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm">Total session time</p>
              <p className="font-medium text-primary">N/A</p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm">Avg session time</p>
              <p className="font-medium text-primary">{dapp.avgTime ? `${dapp.avgTime}s` : 'N/A'}</p>
            </div>
          </div>
        </div>
      </div>

      <div className=" my-10 gap-5 grid grid-cols-12">
        <div className="col-span-12 lg:col-span-5 bg-card rounded-2xl p-6 border">
          <p className="font-medium text-secondary-foreground">Peak usage hours</p>

          <div className="w-full h-56 relative">
            <Image src={"/Frame 198.svg"} alt="" sizes="auto" fill className="object-contain" />
          </div>
        </div>
        <div className="col-span-12 lg:col-span-7 bg-card rounded-2xl p-6 border flex flex-col lg:flex-row justify-between px-6 py-[30px]">
          <div className="w-[286px] h-[260px] relative">
            <Image src={"/Group 1321315495.svg"} alt="" sizes="auto" fill />
          </div>
          <div className="w-fit">
            <p className="font-medium text-secondary-foreground">Geographic reach</p>

            <div className="mt-7 grid grid-cols-2 gap-3">
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#F59D46]" />
                <p>India</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#EECA0A]" />
                <p>Myanmar</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#CA3527]" />
                <p>Vietnam</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#319D47]" />
                <p>Bangladesh</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#FFFFFF]" />
                <p>Indonesia</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#F59D46]" />
                <p>Laos</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#15DCFF]" />
                <p>Philippines</p>
              </div>
              <div className="w-full h-10 flex items-center gap-2 min-w-32">
                <div className="size-[14px] rounded-xs bg-[#0046A5]" />
                <p>Cambodia</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Grid;
