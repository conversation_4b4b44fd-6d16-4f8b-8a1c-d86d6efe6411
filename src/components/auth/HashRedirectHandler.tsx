"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function HashRedirectHandler() {
  const router = useRouter();

  useEffect(() => {
    const handleRedirect = () => {
      if (typeof window === 'undefined') return;

      const hash = window.location.hash;
      const pathname = window.location.pathname;
      


      // Only redirect if we're on the home page with auth-related hash
      if (pathname === '/' && hash) {
        // Check for password reset
        if (hash.includes('access_token') && hash.includes('type=recovery')) {
          window.location.href = `/auth/new-password${hash}`;
          return;
        }

        // Check for password reset errors
        if (hash.includes('error=access_denied') || hash.includes('error_code=otp_expired')) {
          window.location.href = `/auth/new-password${hash}`;
          return;
        }

        // Check for OAuth callback
        if (hash.includes('access_token') && !hash.includes('type=recovery')) {
          window.location.href = `/auth/callback${hash}`;
          return;
        }
      }
    };

    // Multiple attempts to catch the hash
    handleRedirect(); // Immediate
    
    const timer1 = setTimeout(handleRedirect, 50);   // 50ms delay
    const timer2 = setTimeout(handleRedirect, 100);  // 100ms delay
    const timer3 = setTimeout(handleRedirect, 500);  // 500ms delay

    // Listen for hash changes
    const handleHashChange = () => {
      handleRedirect();
    };

    window.addEventListener('hashchange', handleHashChange);

    // Cleanup
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [router]);

  // This component doesn't render anything
  return null;
}
