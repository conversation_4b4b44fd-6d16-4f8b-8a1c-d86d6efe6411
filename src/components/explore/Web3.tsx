"use client";
import Image from "next/image";
import React from "react";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";

interface Web3Item {
  img: string;
  title: string;
  totalViews: number;
}

const NFTS: Web3Item[] = [
  {
    img: "/explore/image.svg",
    title: "GoPro Quik: Video Editor",
    totalViews: 1248,
  },
  {
    img: "/explore/image (1).svg",
    title: "GoPro Quik: Video Editor",
    totalViews: 1248,
  },
  {
    img: "/explore/image (2).svg",
    title: "GoPro Quik: Video Editor",
    totalViews: 1248,
  },
];

const WALLETS: Web3Item[] = [
  {
    img: "/explore/image (3).svg",
    title: "Learn Python Programming",
    totalViews: 1248,
  },
  {
    img: "/explore/image (4).svg",
    title: "ToonMe - cartoons from photosToonMe - cartoons from photos",
    totalViews: 1248,
  },
  {
    img: "/explore/image (5).svg",
    title: "Remini - AI Photo Enhancer",
    totalViews: 1248,
  },
];

const SECURITY: Web3Item[] = [
  {
    img: "/explore/image (6).svg",
    title: "Notein: Handwriting,Notes,PD",
    totalViews: 1248,
  },
  {
    img: "/explore/image (7).svg",
    title: "Microsoft OneNote: Save Notes",
    totalViews: 1248,
  },
  {
    img: "/explore/image (8).svg",
    title: "Photo & Video Editor - Canva",
    totalViews: 1248,
  },
];

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.2,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const Web3 = () => {
  return (
    <div className="container my-10 lg:my-[118px]">
      <p className="font-protest-strike text-3xl uppercase">wEB 3</p>

      {/* Mobile UI */}
      <div className="lg:hidden">
        <Swiper {...swiperConfig}>
          <SwiperSlide>
            <div className="rounded-2xl bg-card border p-5">
              <p className="uppercase font-protest-strike text-xl">nft</p>

              <div className="mt-4 flex flex-col gap-12">
                {NFTS.map((item) => {
                  return (
                    <div key={item.img} className="flex justify-between items-center w-full">
                      <div className="flex items-center gap-3">
                        <div className="size-16 rounded-xl relative">
                          <Image src={item.img} alt="" sizes="auto" fill />
                        </div>
                        <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                      </div>

                      <div className="w-fit">
                        <p className="text-sm text-secondary-foreground">Total views</p>
                        <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="rounded-2xl bg-card border p-5">
              <p className="uppercase font-protest-strike text-xl">wallet</p>

              <div className="mt-4 flex flex-col gap-12">
                {WALLETS.map((item) => {
                  return (
                    <div key={item.img} className="flex justify-between items-center w-full">
                      <div className="flex items-center gap-3">
                        <div className="size-16 rounded-xl relative">
                          <Image src={item.img} alt="" sizes="auto" fill />
                        </div>
                        <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                      </div>

                      <div className="w-fit">
                        <p className="text-sm text-secondary-foreground">Total views</p>
                        <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="rounded-2xl bg-card border p-5">
              <p className="uppercase font-protest-strike text-xl">security</p>

              <div className="mt-4 flex flex-col gap-12">
                {SECURITY.map((item) => {
                  return (
                    <div key={item.img} className="flex justify-between items-center w-full">
                      <div className="flex items-center gap-3">
                        <div className="size-16 rounded-xl relative">
                          <Image src={item.img} alt="" sizes="auto" fill />
                        </div>
                        <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                      </div>

                      <div className="w-fit">
                        <p className="text-sm text-secondary-foreground">Total views</p>
                        <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>

      {/* Desktop UI */}
      <div className="hidden lg:grid my-5 grid-cols-1 lg:grid-cols-3 gap-5">
        <div className="rounded-2xl bg-card border p-5">
          <p className="uppercase font-protest-strike text-xl">nft</p>

          <div className="mt-4 flex flex-col gap-12">
            {NFTS.map((item) => {
              return (
                <div key={item.img} className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-3">
                    <div className="size-16 rounded-xl relative">
                      <Image src={item.img} alt="" sizes="auto" fill />
                    </div>
                    <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                  </div>

                  <div className="w-fit">
                    <p className="text-sm text-secondary-foreground">Total views</p>
                    <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="rounded-2xl bg-card border p-5">
          <p className="uppercase font-protest-strike text-xl">wallet</p>

          <div className="mt-4 flex flex-col gap-12">
            {WALLETS.map((item) => {
              return (
                <div key={item.img} className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-3">
                    <div className="size-16 rounded-xl relative">
                      <Image src={item.img} alt="" sizes="auto" fill />
                    </div>
                    <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                  </div>

                  <div className="w-fit">
                    <p className="text-sm text-secondary-foreground">Total views</p>
                    <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="rounded-2xl bg-card border p-5">
          <p className="uppercase font-protest-strike text-xl">security</p>

          <div className="mt-4 flex flex-col gap-12">
            {SECURITY.map((item) => {
              return (
                <div key={item.img} className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-3">
                    <div className="size-16 rounded-xl relative">
                      <Image src={item.img} alt="" sizes="auto" fill />
                    </div>
                    <p className="max-w-48 line-clamp-2 font-semibold">{item.title}</p>
                  </div>

                  <div className="w-fit">
                    <p className="text-sm text-secondary-foreground">Total views</p>
                    <p className="font-medium">{item.totalViews.toLocaleString("en-US")}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Web3;
