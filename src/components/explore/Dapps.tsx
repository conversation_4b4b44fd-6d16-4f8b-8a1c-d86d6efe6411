"use client";

import DAppCard, { DApp } from "@/components/home/<USER>";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import clsx from "clsx";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { fetcher } from "@/lib/fetcher";
import { Skeleton } from "../ui/skeleton";
import { Separator } from "../ui/separator";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useMemo } from "react";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const filterOptions = ["DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain"];

// Pagination constants
const ITEMS_PER_PAGE = 8;

const DApps = () => {
  const [active, setActive] = useState<string>("DeFi");
  const [dapps, setDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);

  // Reset to page 1 when category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [active]);

  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);
        const response = (await fetcher(
          `/api/v1/dapps/explore?category=${active}&page=${currentPage}&limit=${ITEMS_PER_PAGE}`
        )) as any;

        if (response.success) {
          setDapps(response.data.dapps || []);
          setTotalPages(response.data.pagination.totalPages);
          setTotalItems(response.data.pagination.totalItems);
        }
      } catch (error) {
        // Error handling
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, [active, currentPage]);

  return (
    <div className="container lg:my-24 my-10">
      <div className="flex justify-between items-center">
        <p className="font-protest-strike text-3xl uppercase">Dapps</p>
      </div>

      <div className="flex mt-5 overflow-x-scroll gap-2 md:gap-4">
        {filterOptions.map((option) => (
          <Button
            onClick={() => setActive(option)}
            key={option}
            variant={"outline"}
            className={clsx(
              "h-10 min-w-[105px] text-secondary-foreground/80 px-4 rounded-xl",
              active === option &&
                "hover:bg-primary hover:text-white bg-primary text-white dark:!bg-white dark:text-background dark:hover:text-background font-semibold"
            )}
          >
            {option}
          </Button>
        ))}
      </div>

      <div className="mt-4 lg:mt-10 grid-col-1 lg:grid-cols-4 gap-3 md:gap-5 hidden md:grid">
        {!loading ? (
          dapps.length > 0 ? (
            dapps.map((dapp: DApp) => {
              return <DAppCard key={dapp.id} {...dapp} />;
            })
          ) : (
            <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">
                {active === "DeFi"
                  ? "💰"
                  : active === "NFT"
                    ? "🖼️"
                    : active === "Games"
                      ? "🎮"
                      : active === "Tools"
                        ? "🔧"
                        : active === "Social"
                          ? "👥"
                          : active === "Multi-chain"
                            ? "🔗"
                            : "📱"}
              </div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground">We couldn't find any DApps in the {active} category yet.</p>
            </div>
          )
        ) : (
          Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
              <div className="flex gap-3">
                <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-1/2 w-full rounded-full" />
                  <Skeleton className="h-1/2 w-full rounded-full" />
                </div>
              </div>

              <Separator className="my-1" />

              <div className="grid grid-cols-2 gap-14">
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Total views</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Avg time spend</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-4 md:hidden">
        {!loading ? (
          dapps.length > 0 ? (
            <Swiper {...swiperConfig}>
              {dapps.map((dapp: DApp) => {
                return (
                  <SwiperSlide key={dapp.id}>
                    <DAppCard {...dapp} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
          ) : (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">
                {active === "DeFi"
                  ? "💰"
                  : active === "NFT"
                    ? "🖼️"
                    : active === "Games"
                      ? "🎮"
                      : active === "Tools"
                        ? "🔧"
                        : active === "Social"
                          ? "👥"
                          : active === "Multi-chain"
                            ? "🔗"
                            : "📱"}
              </div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground">We couldn't find any DApps in the {active} category yet.</p>
            </div>
          )
        ) : (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 gap-4">
          <div className="text-sm text-gray-600 dark:text-gray-400 order-2 sm:order-1">
            Showing {(currentPage - 1) * ITEMS_PER_PAGE + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, totalItems)} of{" "}
            {totalItems} dApps
          </div>

          <div className="flex items-center gap-2 order-1 sm:order-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1 || loading}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="w-4 h-4" />
              <span className="hidden sm:inline">Previous</span>
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    disabled={loading}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages || loading}
              className="flex items-center gap-1"
            >
              <span className="hidden sm:inline">Next</span>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DApps;
