"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { getSafeImageSrc } from "@/lib/utils/image";
import Image from "next/image";
import { supabaseClient } from "@/lib/supabase/client";
import slugify from "slugify";
import { ChevronRight } from "lucide-react";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const Web3Categories = () => {
  const [nftDapps, setNftDapps] = useState<DApp[]>([]);
  const [walletDapps, setWalletDapps] = useState<DApp[]>([]);
  const [securityDapps, setSecurityDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCategoryDapps = async () => {
      try {
        setLoading(true);

        // Use supabaseClient directly like PopularDapps
        const { data: nftData } = await supabaseClient.from("dapps").select("*").eq("category", "NFT").limit(3);

        const { data: walletData } = await supabaseClient.from("dapps").select("*").eq("category", "Wallet").limit(3);

        const { data: securityData } = await supabaseClient
          .from("dapps")
          .select("*")
          .eq("category", "Security")
          .limit(3);

        setNftDapps(nftData || []);
        setWalletDapps(walletData || []);
        setSecurityDapps(securityData || []);
      } catch (error) {
        console.error("Error fetching category dapps:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryDapps();
  }, []);

  const CategorySection = ({ title, dapps, categoryLink }: { title: string; dapps: DApp[]; categoryLink: string }) => (
    <div className="rounded-2xl lg:p-6">
      <div className="flex items-center justify-between mb-4 lg:mb-6">
        <h3 className="text-lg font-bold text-black dark:text-white uppercase">{title}</h3>
        <Link href={categoryLink}>
          <Button
            variant="ghost"
            className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
          >
            More <ChevronRight />
          </Button>
        </Link>
      </div>

      <div className="space-y-3 lg:space-y-6">
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex gap-3 p-3 rounded-xl">
              <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl" />
              <div className="flex flex-col gap-2 w-full">
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="text-right">
                <Skeleton className="h-3 w-12 mb-1" />
                <Skeleton className="h-4 w-8" />
              </div>
            </div>
          ))
        ) : dapps.length > 0 ? (
          dapps.slice(0, 3).map((dapp) => (
            <Link
              key={dapp.id}
              href={`/dapps/${slugify(dapp.name || "unnamed-dapp", { lower: true, strict: true })}/${dapp.id}`}
              className="block"
            >
              <div className="flex items-center gap-3 p-2 lg:p-3 rounded-xl cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors bg-[#f5f7f9] dark:bg-card border">
                <div className="size-12 min-w-12 min-h-12 lg:size-16 lg:min-w-16 lg:min-h-16 rounded-xl relative overflow-hidden bg-gray-100 dark:bg-gray-700">
                  {getSafeImageSrc(dapp.logo) ? (
                    <Image
                      src={getSafeImageSrc(dapp.logo)!}
                      alt={dapp.name || "DApp logo"}
                      sizes="auto"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500 text-xs font-medium">
                      No Logo
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-black dark:text-white truncate">{dapp.name}</h4>
                </div>
                <div className="text-right hidden md:block">
                  <p className="text-xs text-gray-500 dark:text-gray-400">Total views</p>
                  <p className="font-semibold text-black dark:text-white">{dapp.total_views || 0}</p>
                </div>
              </div>
            </Link>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No {title.toLowerCase()} DApps found</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="container lg:my-24 my-10">
      <div className="flex flex-col gap-2 lg:mb-8 mb-4">
        <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">WEB 3</h2>
      </div>

      {/* Desktop UI */}
      <div className="lg:grid hidden grid-cols-1 md:grid-cols-3 gap-4  lg:gap-6">
        <CategorySection title="NFT" dapps={nftDapps} categoryLink="/explore/NFT" />
        <CategorySection title="WALLET" dapps={walletDapps} categoryLink="/explore/Wallet" />
        <CategorySection title="SECURITY" dapps={securityDapps} categoryLink="/explore/Security" />
      </div>

      {/* Mobile UI */}
      <div className="lg:hidden">
        <Swiper {...swiperConfig}>
          <SwiperSlide>
            <CategorySection title="NFT" dapps={nftDapps} categoryLink="/explore/NFT" />
          </SwiperSlide>
          <SwiperSlide>
            <CategorySection title="WALLET" dapps={walletDapps} categoryLink="/explore/Wallet" />
          </SwiperSlide>
          <SwiperSlide>
            <CategorySection title="SECURITY" dapps={securityDapps} categoryLink="/explore/Security" />
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  );
};

export default Web3Categories;
