import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

// Types
export interface FetcherOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  baseURL?: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
  transformRequest?: (data: any) => any;
  transformResponse?: (data: any) => any;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export interface FetcherState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface FetcherResponse<T = any> {
  data: T | null;
  error: string | null;
  success: boolean;
}

// Default configuration
const DEFAULT_OPTIONS: Partial<FetcherOptions> = {
  method: 'GET',
  timeout: 30000,
  retries: 0,
  retryDelay: 1000,
  showErrorToast: true,
  showSuccessToast: false,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Error handling utility
const formatError = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.error) return error.error;
  return 'An unexpected error occurred';
};

// Get auth token from cookies
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const cookies = document.cookie.split(';');
    const sessionCookie = cookies.find(cookie => cookie.trim().startsWith('session='));
    return sessionCookie ? sessionCookie.split('=')[1] : null;
  } catch {
    return null;
  }
};

// Sleep utility for retries
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Enhanced Fetcher Hook for API requests
 * 
 * Features:
 * - Automatic authentication token handling
 * - Loading states and error handling
 * - Request/Response transformation
 * - Retry logic with exponential backoff
 * - Toast notifications
 * - Request cancellation
 * - TypeScript support
 */
export function useFetcher<T = any>(defaultOptions?: Partial<FetcherOptions>) {
  const [state, setState] = useState<FetcherState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const request = useCallback(async (
    url: string,
    options: Partial<FetcherOptions> = {}
  ): Promise<FetcherResponse<T>> => {
    // Merge options with defaults
    const config = { ...DEFAULT_OPTIONS, ...defaultOptions, ...options };
    
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    let attempt = 0;
    const maxAttempts = (config.retries || 0) + 1;

    while (attempt < maxAttempts) {
      try {
        // Prepare headers
        const headers = { ...config.headers };
        
        // Add auth token if available
        const token = getAuthToken();
        if (token && !headers.Authorization) {
          headers.Authorization = `Bearer ${token}`;
        }

        // Prepare body
        let body: string | FormData | undefined;
        if (config.body) {
          if (config.transformRequest) {
            body = config.transformRequest(config.body);
          } else if (config.body instanceof FormData) {
            body = config.body;
            // Remove Content-Type for FormData (browser sets it automatically)
            delete headers['Content-Type'];
          } else {
            body = JSON.stringify(config.body);
          }
        }

        // Build full URL
        const fullUrl = config.baseURL ? `${config.baseURL}${url}` : url;

        // Make request with timeout
        const timeoutId = setTimeout(() => {
          abortControllerRef.current?.abort();
        }, config.timeout);

        const response = await fetch(fullUrl, {
          method: config.method,
          headers,
          body,
          signal: abortControllerRef.current.signal,
        });

        clearTimeout(timeoutId);

        // Parse response
        let responseData: any;
        const contentType = response.headers.get('content-type');
        
        if (contentType?.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseData = await response.text();
        }

        // Handle HTTP errors
        if (!response.ok) {
          const errorMessage = formatError(responseData);
          throw new Error(errorMessage);
        }

        // Transform response if needed
        let finalData = responseData;
        if (config.transformResponse) {
          finalData = config.transformResponse(responseData);
        }

        // Extract data from common API response patterns
        if (responseData?.data !== undefined) {
          finalData = responseData.data;
        }

        setState({ data: finalData, loading: false, error: null });

        // Success callbacks and notifications
        if (config.onSuccess) {
          config.onSuccess(finalData);
        }

        if (config.showSuccessToast && config.successMessage) {
          toast.success(config.successMessage);
        }

        return { data: finalData, error: null, success: true };

      } catch (error: any) {
        attempt++;
        
        // If this was the last attempt or error is not retryable
        if (attempt >= maxAttempts || error.name === 'AbortError') {
          const errorMessage = formatError(error);
          
          setState({ data: null, loading: false, error: errorMessage });

          // Error callbacks and notifications
          if (config.onError) {
            config.onError(error);
          }

          if (config.showErrorToast && error.name !== 'AbortError') {
            toast.error(errorMessage);
          }

          return { data: null, error: errorMessage, success: false };
        }

        // Wait before retry with exponential backoff
        const delay = config.retryDelay! * Math.pow(2, attempt - 1);
        await sleep(delay);
      }
    }

    // This should never be reached, but TypeScript requires it
    return { data: null, error: 'Maximum retries exceeded', success: false };
  }, [defaultOptions]);

  // Cancel any pending requests
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setState(prev => ({ ...prev, loading: false }));
    }
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    request,
    cancel,
    reset,
  };
}

/**
 * Convenience hooks for common HTTP methods
 */
export function useGet<T = any>(defaultOptions?: Partial<FetcherOptions>) {
  const fetcher = useFetcher<T>({ ...defaultOptions, method: 'GET' });
  
  const get = useCallback((url: string, options?: Partial<FetcherOptions>) => {
    return fetcher.request(url, { ...options, method: 'GET' });
  }, [fetcher]);

  return { ...fetcher, get };
}

export function usePost<T = any>(defaultOptions?: Partial<FetcherOptions>) {
  const fetcher = useFetcher<T>({ ...defaultOptions, method: 'POST' });
  
  const post = useCallback((url: string, body?: any, options?: Partial<FetcherOptions>) => {
    return fetcher.request(url, { ...options, method: 'POST', body });
  }, [fetcher]);

  return { ...fetcher, post };
}

export function usePut<T = any>(defaultOptions?: Partial<FetcherOptions>) {
  const fetcher = useFetcher<T>({ ...defaultOptions, method: 'PUT' });
  
  const put = useCallback((url: string, body?: any, options?: Partial<FetcherOptions>) => {
    return fetcher.request(url, { ...options, method: 'PUT', body });
  }, [fetcher]);

  return { ...fetcher, put };
}

export function useDelete<T = any>(defaultOptions?: Partial<FetcherOptions>) {
  const fetcher = useFetcher<T>({ ...defaultOptions, method: 'DELETE' });
  
  const del = useCallback((url: string, options?: Partial<FetcherOptions>) => {
    return fetcher.request(url, { ...options, method: 'DELETE' });
  }, [fetcher]);

  return { ...fetcher, delete: del };
}
