import { useCallback } from 'react';
import { useFetcher, useGet, usePost } from './useFetcher';
import { API_ENDPOINTS, SUCCESS_MESSAGES } from '@/constants';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DApp {
  id: string;
  name: string;
  category: string;
  description: string;
  live_url: string;
  logo: string;
  user_id: string;
  plan_type?: string;
  paymentid?: string;
  validtill?: string;
  avgTime: number;
  total_views: number;
  created_at: string;
  updated_at?: string;
}

export interface PricingPlan {
  id: number;
  name: string;
  price_usd: number;
  billing_period: string;
  features: string[];
  is_active: boolean;
  stripe_price_id?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at?: string;
}

export interface CouponValidationResponse {
  valid: boolean;
  coupon?: {
    id: string;
    code: string;
    isFree: boolean;
  };
  error?: string;
  message?: string;
}

export interface CheckoutSessionResponse {
  success: boolean;
  id?: string; // Stripe session ID
  sessionId?: string; // Duplicate of id for compatibility
  url?: string; // Direct URL to Stripe checkout
  freeWithCoupon?: boolean;
  couponUsed?: string;
  redirectUrl?: string;
  error?: string;
  planDetails?: {
    name: string;
    price: number;
    features: string[];
  };
}

/**
 * Authentication API hooks
 */
export function useAuthApi() {
  const signInFetcher = usePost<ApiResponse<{ user: User; session: any }>>({
    showErrorToast: true,
    showSuccessToast: true,
    successMessage: SUCCESS_MESSAGES.SIGN_IN_SUCCESS,
  });

  const signUpFetcher = usePost<ApiResponse<User>>({
    showErrorToast: true,
    showSuccessToast: true,
    successMessage: SUCCESS_MESSAGES.SIGN_UP_SUCCESS,
  });

  const sessionFetcher = useGet<ApiResponse<User>>({
    showErrorToast: false, // Don't show error toast for session checks
  });

  const signIn = useCallback(async (email: string, password: string) => {
    return signInFetcher.post(API_ENDPOINTS.AUTH.SIGN_IN, { email, password });
  }, [signInFetcher]);

  const signUp = useCallback(async (email: string, password: string) => {
    return signUpFetcher.post(API_ENDPOINTS.AUTH.SIGN_UP, { email, password });
  }, [signUpFetcher]);

  const getSession = useCallback(async () => {
    return sessionFetcher.get(API_ENDPOINTS.AUTH.SESSION);
  }, [sessionFetcher]);

  return {
    signIn: {
      ...signInFetcher,
      execute: signIn,
    },
    signUp: {
      ...signUpFetcher,
      execute: signUp,
    },
    session: {
      ...sessionFetcher,
      execute: getSession,
    },
  };
}

/**
 * DApps API hooks
 */
export function useDAppsApi() {
  const listFetcher = useGet<PaginatedResponse<DApp>>({
    showErrorToast: true,
  });

  const createFetcher = usePost<CheckoutSessionResponse>({
    showErrorToast: true,
    showSuccessToast: false, 
  });

  const getDApps = useCallback(async (params: {
    page?: number;
    limit?: number;
    category?: string;
  } = {}) => {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.category) searchParams.set('category', params.category);


    const url = `${API_ENDPOINTS.DAPPS.LIST}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return listFetcher.get(url);
  }, [listFetcher]);

  const createDApp = useCallback(async (dappData: {
    dappName: string;
    category: string;
    description: string;
    liveUrl: string;
    logo: string;
    planId: number;
    userId: string;
    couponCode?: string;
  }) => {
    // Convert to JSON for the checkout-session endpoint
    return createFetcher.post(API_ENDPOINTS.PAYMENT.CHECKOUT_SESSION, dappData);
  }, [createFetcher]);

  return {
    list: {
      ...listFetcher,
      execute: getDApps,
    },
    create: {
      ...createFetcher,
      execute: createDApp,
    },
  };
}

/**
 * Pricing Plans API hooks
 */
export function usePricingApi() {
  const fetcher = useGet<{ success: boolean; plans: PricingPlan[] }>({
    showErrorToast: true,
  });

  const getPricingPlans = useCallback(async () => {
    return fetcher.get(API_ENDPOINTS.PRICING.PLANS);
  }, [fetcher]);

  return {
    ...fetcher,
    execute: getPricingPlans,
  };
}

/**
 * Coupons API hooks
 */
export function useCouponsApi() {
  const fetcher = usePost<CouponValidationResponse>({
    showErrorToast: true,
  });

  const validateCoupon = useCallback(async (couponCode: string) => {
    return fetcher.post(API_ENDPOINTS.COUPONS.VALIDATE, { couponCode });
  }, [fetcher]);

  return {
    ...fetcher,
    execute: validateCoupon,
  };
}

/**
 * Generic Supabase query hook (for direct database queries)
 */
export function useSupabaseQuery<T = any>() {
  const fetcher = useFetcher<T>({
    showErrorToast: true,
  });

  const query = useCallback(async (
    table: string,
    options: {
      select?: string;
      filters?: Record<string, any>;
      limit?: number;
      order?: { column: string; ascending?: boolean };
      single?: boolean;
    } = {}
  ) => {
    // This would be used for direct Supabase queries
    // For now, we'll use it as a placeholder for custom queries
    const { select = '*', filters = {}, limit, order, single } = options;
    
    // Build query parameters
    const params = new URLSearchParams();
    params.set('table', table);
    params.set('select', select);
    
    if (Object.keys(filters).length > 0) {
      params.set('filters', JSON.stringify(filters));
    }
    
    if (limit) params.set('limit', limit.toString());
    if (order) params.set('order', JSON.stringify(order));
    if (single) params.set('single', 'true');

    return fetcher.request(`/api/v1/query?${params.toString()}`);
  }, [fetcher]);

  return {
    ...fetcher,
    query,
  };
}

/**
 * File upload hook
 */
export function useFileUpload() {
  const fetcher = useFetcher({
    showErrorToast: true,
    showSuccessToast: true,
    successMessage: SUCCESS_MESSAGES.UPLOAD_SUCCESS,
  });

  const upload = useCallback(async (
    file: File,
    endpoint: string,
    additionalData?: Record<string, any>
  ) => {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    return fetcher.request(endpoint, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header for FormData
      headers: {},
    });
  }, [fetcher]);

  return {
    ...fetcher,
    upload,
  };
}

/**
 * Combined API hook that provides access to all API endpoints
 */
export function useApiClient() {
  const auth = useAuthApi();
  const dapps = useDAppsApi();
  const pricing = usePricingApi();
  const coupons = useCouponsApi();
  const supabase = useSupabaseQuery();
  const fileUpload = useFileUpload();

  return {
    auth,
    dapps,
    pricing,
    coupons,
    supabase,
    fileUpload,
  };
}

/**
 * Hook for form field disabling during loading states
 * Provides consistent utilities for disabling form interactions during API requests
 */
export const useFormDisabling = (loading: boolean) => {
  return {
    // Input field props
    getInputProps: () => ({
      disabled: loading,
    }),
    
    // Button props for interactive elements
    getInteractiveProps: () => ({
      disabled: loading,
      className: loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
    }),
    
    // Link props for navigation elements
    getLinkProps: () => ({
      className: loading ? 'pointer-events-none opacity-50' : '',
    }),
    
    // Generic element props
    getElementProps: () => ({
      className: loading ? 'opacity-50' : '',
    }),
    
    // Eye icon toggle props for password fields
    getPasswordToggleProps: () => ({
      onClick: loading ? undefined : undefined, // Will be overridden by actual toggle function
      className: `${loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`,
    }),
    
    // Check if form is disabled
    isDisabled: loading,
  };
};
