import * as React from 'react'
import {
  Body,
  Container,
  Column,
  Head,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Text,
  Tailwind,
  Font,
  render,
} from '@react-email/components'

interface VercelInviteUserEmailProps {
  name?: string
  previewText?: string
  link?: {
    href: string
    text: string
  }
  body?: React.ReactElement
}

const title = 'BNRY dApps'
const email = '<EMAIL>'

export const CommonTemplate = ({
  name,
  previewText,
  link,
  body,
}: VercelInviteUserEmailProps) => {
  return (
    <Html>
      <Head>
        <Font
          fontFamily="IBM Plex Mono"
          fallbackFontFamily="monospace"
          webFont={{
            url: 'https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap',
            format: 'woff2',
          }}
          fontWeight={400}
          fontStyle="normal"
        />
        <Font
          fontFamily="Protest Strike"
          fallbackFontFamily="sans-serif"
          webFont={{
            url: 'https://fonts.googleapis.com/css2?family=Protest+Strike&display=swap',
            format: 'woff2',
          }}
          fontWeight={400}
          fontStyle="normal"
        />
      </Head>
      {previewText && <Preview>{previewText}</Preview>}
      <Tailwind>
        <Body className="mx-auto my-auto bg-white px-2 font-sans">
          <Container className="mx-auto my-[40px] max-w-[600px] rounded border border-solid border-[#eaeaea]">
            <Section className="mx-auto mt-10 w-[90%] border-b border-white/20">
              <div className="text-center pb-5">
                <Text
                  className="text-2xl font-bold m-0"
                  style={{
                    fontFamily: 'Protest Strike, sans-serif',
                    fontSize: '1.5rem',
                    color: '#000',
                    textDecoration: 'none',
                    lineHeight: '1.2'
                  }}
                >
                  BNRY<br />dAPPS
                </Text>
              </div>
            </Section>
            <Section className="mx-auto w-[90%] text-start">
              <Text className="text-[16px]">
                {name && <strong>Dear {name}</strong>}
              </Text>
            </Section>
            {body && body}
            <Section className="mx-auto w-[90%] text-start">
              <Text className="text-[16px] leading-[24px] ">
                Sincerely,
                <br />
                The Binary Holdings Team
              </Text>
            </Section>
            {link && (
              <Section className="mx-auto mt-10 w-[90%] text-start">
                <Link
                  className="cursor-pointer rounded bg-[#00ff85] px-5 py-3 text-center text-[16px] font-semibold text-black no-underline"
                  href={link.href}
                >
                  {link.text}
                </Link>
              </Section>
            )}

            <Section className="mx-auto w-[90%] border-t border-white/20">
              <Text className="uppercase my-0 mt-10">contact us</Text>
              <Text className="text-xl my-0 mb-10">
                <a className="text-lg " href={`mailto:${email}`}>
                  {email}
                </a>
              </Text>

              <Row className="mx-auto my-10 w-1/3">
                <Column align="center">
                  <Link
                    className="flex h-10 w-10 items-center justify-center rounded-full border-[0.5px] border-solid border-[#363636] bg-[#00ff85] p-0 text-black"
                    href="https://t.me/tbhofficialchat"
                  >
                    <Img
                      className="m-auto"
                      alt="Telegram"
                      width={20}
                      height={20}
                      src="https://www.bnrydapps.com/telegram.png"
                    />
                  </Link>
                </Column>
                <Column align="center">
                  <Link
                    className="flex h-10 w-10 items-center justify-center rounded-full border-[0.5px] border-solid border-[#363636] bg-[#00ff85] p-0 text-black"
                    href="https://x.com/thebinaryhldgs"
                  >
                    <Img
                      alt="Twitter"
                      width={20}
                      height={20}
                      src="https://www.bnrydapps.com/twitter.png"
                      className="m-auto"
                    />
                  </Link>
                </Column>
                <Column align="center">
                  <Link
                    className="flex h-10 w-10 items-center justify-center rounded-full border-[0.5px] border-solid border-[#363636] bg-[#00ff85] p-0 text-black"
                    href="https://discord.gg/wCXJmTBGr2"
                  >
                    <Img
                      alt="Discord"
                      width={20}
                      height={20}
                      src="https://www.bnrydapps.com/discord.png"
                      className="m-auto"
                    />
                  </Link>
                </Column>
              </Row>

              <Row className="w-full text-[10px] ">
                <Column align="left" className="pb-5">
                  <a className="text-xs">{email}</a>
                </Column>
                <Column align="right" className="pb-5">
                  <p className="text-xs">
                    ©{new Date().getFullYear()} The Binary Holdings. All rights
                    reserved.
                  </p>
                </Column>
              </Row>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}

export default CommonTemplate
