import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import {
  addNewDapp,
  updateDappSubscription,
} from "@/services/dapp.service";
import { HTTP_STATUS, ERROR_MESSAGES } from "@/constants";
import blockchainService from "@/blockchain/blockchainService";
import { lowerCase } from "lodash";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    if (body && body.hash) {
      const bc = new blockchainService();
      const verifyHash = await bc.getBNRYTxInfo(body.hash);
      if (Number(verifyHash?.value).toFixed(5) !== Number(body?.value).toFixed(5) || lowerCase(verifyHash?.to)!== lowerCase(body?.to) || lowerCase(verifyHash?.from) !== lowerCase(body?.from)) {
        return NextResponse.json(
          { error: ERROR_MESSAGES.INVALID_TRANSACTION },
          { status: HTTP_STATUS.BAD_REQUEST }
        );
      }
      const validTill = new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      );

      try {
        const { data, error } = body?.dappId
          ? await updateDappSubscription(
              body?.dappId,
              validTill,
              null,
              body?.hash
            )
          : await addNewDapp(
              { metadata: body } as Stripe.Checkout.Session,
              validTill
            );

        if (error) {
          throw error;
        }

        return NextResponse.json({
          received: true,
          success: true,
          dappId: data?.id,
          data: data
        });
      } catch (error) {
        throw error;
      }
    } else {
    }
    return NextResponse.json({ received: true });
  } catch (error) {
    return NextResponse.json(
      { error: ERROR_MESSAGES.SERVER_ERROR },
      { status: HTTP_STATUS.BAD_REQUEST }
    );
  }
}
