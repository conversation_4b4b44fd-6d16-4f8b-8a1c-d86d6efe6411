import { NextResponse } from 'next/server'
import { getAllPricingPlans } from '@/services/pricing.service'

export async function GET() {
  try {
    const { data: pricingPlans, error } = await getAllPricingPlans()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch pricing plans' }, 
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      plans: pricingPlans,
      success: true 
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
