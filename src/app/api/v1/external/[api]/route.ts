import { NextResponse } from 'next/server';

const allowedParams = ['online-users', 'session', 'ratings'];

export async function GET(req: Request, { params }: { params: Promise<{ api: string }> }) {
  const { api } = await params;
  const url = new URL(req.url);
  const dappId = url.searchParams.get('dappId');

  if (!allowedParams.includes(api)) {
    return NextResponse.json({ error: 'Invalid API parameter' }, { status: 400 });
  }

  if (!dappId) {
    return NextResponse.json({ error: 'Missing dappId parameter' }, { status: 400 });
  }

  try {
    const response = await fetch(`${process.env.ONE_DIGITAL_API}api/v1/game/${api}/${dappId}`);
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
