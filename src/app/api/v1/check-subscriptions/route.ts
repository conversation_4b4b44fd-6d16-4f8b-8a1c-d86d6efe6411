import EmailBody from '@/email-templates/EmailBodyHandler'
import { IGame } from '@/store/singleGameStore'
import { doEmail } from '../emails'
import moment from 'moment'
import { getUsersById } from '@/services/profile.service'
import { expiredDapps } from '@/services/dapp.service'
import { NextRequest, NextResponse } from 'next/server'

const profiles: Record<string, any> = {}

const hourToDays = (hours: number) => {
  return Math.floor(hours / 24)
}

const sendEmail = async () => {
  const period = Number(process.env?.NEXT_PUBLIC_EXP_DAYS || 3)
  const dapps = await expiredDapps(period)
  if (dapps?.length) {
    const emailPromises = dapps.map(async (dapp: IGame) => {
      const hours = moment(dapp?.validTill || moment().toNow()).diff(moment(), 'hours')
      const days = hourToDays(hours)

      if (dapp?.validTill && ((days > 0 && days <= period) || !days)) {
        let profile = profiles[dapp.createdBy]
        if (!profile) {
          const { user } = await getUsersById(dapp.createdBy)
          if (user) {
            profiles[dapp.createdBy] = user
            profile = user
          }
        }

        const body = EmailBody.renewGameSubscription(dapp, profile)
        const newBody = await body.props.children
        const validTill = moment(dapp.validTill).diff(moment(), 'days')
        const subject = dapp.validTill
          ? `Subscription for ${dapp.title} will expire in ${validTill} days`
          : `Subscription Expired for ${dapp.title}`

        if (profile?.email) {
          await doEmail(profile.email, subject, newBody)
        }
      }
    })

    await Promise.all(emailPromises)
    return dapps.length
  } else {
    return 0
  }
}

export async function GET(req: NextRequest) {
  console.log('Subscription check cron job started')
  const secret = process.env.CRON_SECRET
  const authHeader = req.headers.get('authorization')

  if (authHeader !== `Bearer ${secret}`) {
    console.log('Unauthorized')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
  }

  try {
    const count = await sendEmail()
    console.log('Notifications sent successfully')
    return NextResponse.json({
      message: `Notifications sent for ${count} game subscriptions`,
    }, { status: 200 })
  } catch (error) {
    console.log('Error sending notifications')
    return NextResponse.json({ error }, { status: 500 })
  }
}
