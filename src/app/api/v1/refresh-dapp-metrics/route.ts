import { supabaseClient } from '@/lib/supabase/client'
import { NextRequest, NextResponse } from 'next/server'

const apiEndpoint = process.env.ONE_DIGITAL_API
const dTable = `dapps`

const updateRatings = async (dappId: string) => {
  let reviewCount = 0
  let ratingCount = 0
  const apiReq = await fetch(`${apiEndpoint}api/v1/game/ratings/${dappId}`)
  const data = await apiReq.json()
  data.forEach((d: any) => {
    if (d.rating) {
      reviewCount += d.rating
      ratingCount++
    }
  })
  const avgRating = ratingCount ? reviewCount / ratingCount : 0
  await supabaseClient
    .from(dTable)
    .update({ avgRating, ratingCount })
    .eq('id', dappId)
  return { avgRating, ratingCount, updatedAt: new Date() }
}

const updateSession = async (dappId: string) => {
  const apiReq = await fetch(`${apiEndpoint}api/v1/game/session/${dappId}`)
  const data = await apiReq.json()
  const {
    total_count: playCount,
    avg_session_time_today: avgTime,
    total_session_time: totalTimePlayed,
  } = data
  await supabaseClient
    .from(dTable)
    .update({ avgTime, playCount, totalTimePlayed, updatedAt: new Date() })
    .eq('id', dappId)
  return { playCount, avgTime, totalTimePlayed }
}

export async function GET(req: NextRequest) {
  console.log('Dapp metrics update cron job started')
  const secret = process.env.CRON_SECRET

  const authHeader = req.headers.get('authorization')
  if (authHeader !== `Bearer ${secret}`) {
    console.log('Unauthorized')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
  }

  try {
    const { data: dapps, error } = await supabaseClient.from(dTable).select('*')
    if (dapps?.length) {
      for (const dapp of dapps) {
        await updateRatings(dapp.id)
        await updateSession(dapp.id)
      }
      console.log('Dapps metrics updated successfully')
      return NextResponse.json({ message: 'Ratings updated successfully' }, { status: 200 })
    } else {
      return NextResponse.json({ message: 'No dapps found' }, { status: 200 })
    }
  } catch (error) {
    console.log('Error updating dapp metrics:', error)
    return NextResponse.json({ error }, { status: 500 })
  }
}
