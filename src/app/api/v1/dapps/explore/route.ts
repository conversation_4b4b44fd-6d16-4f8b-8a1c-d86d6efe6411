import { NextRequest, NextResponse } from 'next/server';
import { serverClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { _Error, _Success } from '@/lib/response';

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = serverClient(cookieStore);
    const { searchParams } = new URL(req.url);
    
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '8');
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('dapps')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    if (category && category !== 'All') {
      query = query.eq('category', category);
    }

    // Add search functionality
    if (search && search.trim()) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: dapps, error, count } = await query;

    if (error) {
      return _Error('Failed to fetch DApps', 500);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return _Success({
      dapps: dapps || [],
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: count || 0,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      category
    });

  } catch (error) {
    return _Error('Internal server error', 500);
  }
}
