import { NextRequest, NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/client';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category');
    const limit = parseInt(searchParams.get('limit') || '20');


    let query = supabaseClient
      .from('dapps')
      .select('*')
      .limit(limit)
      .order('created_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    const { data: dapps, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch DApps', details: error },
        { status: 500 }
      );
    }

    // count: dapps?.length || 0,
    // category,
    // firstDApp: dapps?.[0] ? {
    //   id: dapps[0].id,
    //   name: dapps[0].name,
    //   category: dapps[0].category,
    //   user_id: dapps[0].user_id,
    //   paymentid: dapps[0].paymentid,
    //   created_at: dapps[0].created_at
    // } : null

    return NextResponse.json({
      success: true,
      data: dapps || [],
      count: dapps?.length || 0,
      category,
      limit
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
