import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';
export const dynamic = 'force-dynamic';

const RESEND_API_KEY = process.env.RESEND_API_KEY;
const from = 'no-reply <<EMAIL>>';

export const doEmail = async (to: string, subject: string, body: string) => {
  const res = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${RESEND_API_KEY}`,
    },
    body: JSON.stringify({
      from,
      to,
      subject,
      html: body,
    }),
  });
  return res;
}

export async function POST(req: NextRequest) {
  const data = await req.json();
  const { to, body, subject } = data;
  try {
    const res: Response = await doEmail(to, subject, body);
    if (res.ok) {
      const data = await res.json();
      return NextResponse.json(data);
    } else {
      return NextResponse.error();
    }
  } catch (error) {
    return NextResponse.error();
  }
}

export default POST;