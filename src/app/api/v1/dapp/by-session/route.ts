import { NextRequest, NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/client';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }


    const { data: dapp, error } = await supabaseClient
      .from('dapps')
      .select('id, name, description, logo, live_url, plan_type')
      .eq('paymentid', sessionId)
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'DApp not found' },
        { status: 404 }
      );
    }

    if (!dapp) {
      return NextResponse.json(
        { error: 'DApp not found' },
        { status: 404 }
      );
    }


    const response = {
      success: true,
      dapp: {
        id: dapp.id,
        name: dapp.name,
        description: dapp.description,
        logo: dapp.logo,
        live_url: dapp.live_url,
        plan_type: dapp.plan_type,
      }
    };


    return NextResponse.json(response);

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
