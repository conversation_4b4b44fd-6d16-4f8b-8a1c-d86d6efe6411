import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

const changePassword = async (email: string, oldPassword: string, newPassword: string) => {
  try {
    // Verify old password by signing in
    const { data, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password: oldPassword,
    });

    if (signInError) {
      return { error: signInError.message };
    }

    // Update password using the session from sign in
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (updateError) {
      return { error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    return { error: 'Failed to change password' };
  }
};

const getUserFromToken = async (accessToken: string) => {
  try {
    // Set session with the token
    const { data, error } = await supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: '', // Not needed for user retrieval
    });

    if (error) {
      return null;
    }

    // Get user from session
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  } catch (error) {
    return null;
  }
};

export async function POST(req: NextRequest) {
  try {
    const accessToken = req.cookies.get("session")?.value || req.headers.get("Authorization")?.split(" ")[1];
    if (!accessToken) {
      return _Error("Invalid session");
    }
    
    const user = await getUserFromToken(accessToken);
    if (!user) {
      return _Error("Invalid session");
    }
    
    const { password, newPassword } = await req.json();
    if (!password || !newPassword) {
      return _Error("Current password and new password are required");
    }
    
    if (!user.email) {
      return _Error("User email not found");
    }
    
    const result = await changePassword(user.email, password, newPassword);
    
    if (result.error) {
      return _Error(result.error);
    }
    
    return _Success("Password changed successfully");
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}
