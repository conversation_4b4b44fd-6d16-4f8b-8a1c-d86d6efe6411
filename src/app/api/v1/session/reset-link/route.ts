import { _Error, _Success } from "@/lib/response";
import { URL_CONFIG } from "@/constants";
import { NextRequest } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

const sendResetLink = async (email: string, currentDomain: string) => {
  try {
    // Create a fresh Supabase client for this request
    const { createClient } = await import('@supabase/supabase-js');
    const customSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Smart domain detection and redirect URL configuration
    const smartDomain = URL_CONFIG.getCurrentDomain(currentDomain.split('://')[1]);
    const redirectUrl = `${smartDomain}${URL_CONFIG.RESET_PASSWORD_PATH}`;

    // Send reset email
    const { data, error } = await customSupabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl
    });

    if (error) {
      if (error.message && error.message.includes('rate limit')) {
        return { error: 'Too many reset requests. Please wait a few minutes before trying again.' };
      }
      return { error: error.message || 'Failed to send reset link' };
    }

    return { success: true, data };
  } catch (error) {
    return { error: 'Failed to send reset link' };
  }
};

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();
    
    if (!email) {
      return _Error("Email is required");
    }
    if (!email.includes("@")) {
      return _Error("Invalid email address");
    }

    const host = req.headers.get("host");
    const protocol = req.headers.get("x-forwarded-proto") ?? "http";
    let currentDomain = `${protocol}://${host}`;

    // Force localhost for development
    if (host?.includes('localhost') || host?.includes('127.0.0.1')) {
      currentDomain = `http://${host}`;
    }

    const result = await sendResetLink(email, currentDomain);
    
    if (result.error) {
      return _Error(result.error, 400);
    }
    
    return _Success("Password reset link sent successfully. Check your email.");
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}
