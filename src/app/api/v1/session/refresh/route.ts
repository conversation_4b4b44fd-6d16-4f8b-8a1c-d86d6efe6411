import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";
export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
  try {
    const { refresh_token } = await req.json();
    
    if (!refresh_token) {
      return _Error("Refresh token is required", 400);
    }

    // Refresh the session using the refresh token
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token
    });

    if (error || !data.session) {
      return _Error("Invalid or expired refresh token", 401);
    }

    // Return the new session data
    const response = _Success({
      session: {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
        user: data.session.user
      }
    });

    // Set new cookies with the refreshed tokens
    response.headers.set("Set-Cookie", `session=${data.session.access_token}; Path=/; SameSite=Strict; Secure; Max-Age=86400`);
    response.headers.append("Set-Cookie", `refresh=${data.session.refresh_token}; Path=/; SameSite=Strict; Secure; Max-Age=604800`);

    return response;
  } catch (error) {
    return _Error("Internal server error", 500);
  }
}
