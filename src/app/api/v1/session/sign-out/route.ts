import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";
export const dynamic = "force-dynamic";

const signOut = async (accessToken?: string) => {
  try {
    if (accessToken) {
      await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: '',
      });
    }
    
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      return _Error("Failed to sign out from server");
    }
    
    return "Sign out successful";
  } catch (error) {
    return "Sign out successful"; // Always succeed for client-side logout
  }
};

export async function GET(req: NextRequest) {
  try {
    const accessToken = req.cookies.get("session")?.value;
    const result = await signOut(accessToken);
    
    if (result instanceof NextResponse) {
      return result;
    }
    
    const response = _Success(result);
    response.headers.set("Set-Cookie", `session=; Path=/; HttpOnly; Secure; SameSite=Strict; Max-Age=0`);
    response.headers.append("Set-Cookie", `refresh=; Path=/; HttpOnly; Secure; SameSite=Strict; Max-Age=0`);
    return response;
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}

export async function POST(req: NextRequest) {
  try {
    const accessToken = req.cookies.get("session")?.value;
    const result = await signOut(accessToken);
    
    if (result instanceof NextResponse) {
      return result;
    }
    
    const response = _Success(result);
    response.headers.set("Set-Cookie", `session=; Path=/; HttpOnly; Secure; SameSite=Strict; Max-Age=0`);
    response.headers.append("Set-Cookie", `refresh=; Path=/; HttpOnly; Secure; SameSite=Strict; Max-Age=0`);
    return response;
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}
