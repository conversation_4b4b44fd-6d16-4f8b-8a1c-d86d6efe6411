// app/api/v1/session/route.ts
import { _Error, _Success } from "@/lib/response";
import { serverClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const cookieStore = await cookies();
  const supabase = serverClient(cookieStore);
  const session = req.cookies.get("session")?.value;
  if (!session) {
   return _Error("Session Token is required");
  }

  const { data: { user }, error } = await supabase.auth.getUser(session);

  if (error || !user) {
    return _Error("User not found or session expired", 401);
  }

  return _Success(user);
}
