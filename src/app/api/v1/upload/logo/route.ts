import { NextRequest, NextResponse } from "next/server";
import { uploadDapp<PERSON>ogo, getDappLogoUrl } from "@/lib/supabase/storage";
import { generateSafeFilename } from "@/lib/utils/image";
import { serverClient } from "@/lib/supabase/server";
import { cookies } from "next/headers";

export async function POST(req: NextRequest) {
  try {
    // Verify user authentication
    const cookieStore = await cookies();
    const supabase = serverClient(cookieStore);
    const sessionToken = req.cookies.get("session")?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Verify the session
    const { data: { user }, error: authError } = await supabase.auth.getUser(sessionToken);

    if (authError || !user) {
      return NextResponse.json(
        { error: "Invalid session" },
        { status: 401 }
      );
    }

    // Parse the form data
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const dappName = formData.get("dappName") as string;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    if (!dappName) {
      return NextResponse.json(
        { error: "DApp name is required" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed." },
        { status: 400 }
      );
    }

    // Validate file size (2MB limit)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 2MB." },
        { status: 400 }
      );
    }

    // Generate safe filename
    const fileName = generateSafeFilename(dappName);

    // Upload the file using service role
    const { data, error } = await uploadDappLogo(file, fileName);

    if (error) {
      return NextResponse.json(
        { error: "Failed to upload file" },
        { status: 500 }
      );
    }

    // Get the public URL
    const publicUrl = getDappLogoUrl(fileName);

    return NextResponse.json({
      success: true,
      data: {
        fileName,
        fullPath: data?.fullPath,
        publicUrl,
      }
    });

  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
