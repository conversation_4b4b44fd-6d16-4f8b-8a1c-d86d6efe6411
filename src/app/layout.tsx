import type { <PERSON><PERSON><PERSON> } from "next";
import { Protest_Strike, Poppins } from "next/font/google";
import clsx from "clsx";
import { CookiesProvider } from "next-client-cookies/server";

import "@/styles/globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import HashRedirectHandler from "@/components/auth/HashRedirectHandler";

export const metadata: Metadata = {
  title: "BNRY dApps | Decentralized Apps Made Easy",
  description: "Explore and deploy decentralized applications seamlessly with BNRY dApps — your gateway to Web3 innovation.",
  keywords: [
    "BNRY", 
    "dApps", 
    "Web3", 
    "blockchain", 
    "smart contracts", 
    "Ethereum", 
    "Optimism", 
    "decentralized applications"
  ],
  metadataBase: new URL("https://bnrydapps.com"),
};


const protestStrike = Protest_Strike({
  subsets: ["latin"],
  variable: "--font-protest-strike",
  display: "swap",
  weight: ["400"],
  style: ["normal"],
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body
        className={clsx("max-w-screen overflow-x-hidden", protestStrike.variable, poppins.variable, "font-poppins")}
      >
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <CookiesProvider>
            <HashRedirectHandler />
            <Toaster position="top-right" />
            {children}
          </CookiesProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
