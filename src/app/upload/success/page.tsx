import Logo from "@/components/Logo";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

const Success = () => {
  return (
    <div className="grid grid-cols-3 h-screen w-screen">
      <div className="col-span-1 relative">
        <Logo />
        <div className="size-full relative">
          <Image
            src="/congratulations_bg.webp"
            alt="Congratulations Background"
            sizes="auto"
            fill
            className="object-cover object-bottom"
          />
        </div>
      </div>

      <div className="col-span-2 flex items-center justify-center">
        <div className="w-[520px] bg-card rounded-2xl px-6 mx-4 lg:px-[60px] py-[30px] shadow-xl">
          <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
            Payment Successful!
          </h1>

          <p className="text-muted-foreground text-sm mt-2 leading-relaxed">
            Thank you, <span className="font-semibold text-foreground">User Name</span>.<br />
            Your payment has been successfully processed, and your DApp is now ready to launch.
          </p>

          <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
            You can now start managing your DApp, monitor analytics, and reach users worldwide.
          </p>

          <Link href="/dashboard">
            <Button className="bg-green dark:text-white mt-7 h-11 px-7 w-full">
              Go to Dapp
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Success;
