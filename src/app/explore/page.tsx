"use client";

import GameFi from "@/components/explore/Gamefi";
import Hero from "@/components/explore/Hero";
import TopDApps from "@/components/explore/TopDApps";
import Trending from "@/components/explore/Trending";
import Web3Categories from "@/components/explore/Web3Categories";
import Footer from "@/components/home/<USER>";
import PopularDapps from "@/components/home/<USER>";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

const ExploreContent = () => {
  const searchParams = useSearchParams();
  const headerParam = searchParams.get('header');
  const showHeader = headerParam !== 'false';



  return (
    <div>
      <Hero showHeader={showHeader} />
      <Trending isExplorePage={true} />
      <Web3Categories />
      <PopularDapps isExplorePage={true} />
      <TopDApps isExplorePage={true} />
      <GameFi isExplorePage={true} />
      {showHeader && <Footer />}
    </div>
  );
};

const Explore = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ExploreContent />
    </Suspense>
  );
};

export default Explore;
