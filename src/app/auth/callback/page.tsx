"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader, CheckCircle2, AlertCircle } from 'lucide-react';
import { supabaseClient } from '@/lib/supabase/client';
import { storeAuthTokens } from '@/lib/auth/utils';
import { usePost } from '@/hooks/useFetcher';
import useGetUser from '@/hooks/useGetUser';

export default function AuthCallback() {
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');
  const oauthCallbackFetcher = usePost();
  const { refreshUser } = useGetUser();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Check for email confirmation in URL hash
        const hash = window.location.hash;
        if (hash.includes('access_token') && hash.includes('type=signup')) {

          const params = new URLSearchParams(hash.slice(1));
          const accessToken = params.get('access_token');
          const refreshToken = params.get('refresh_token');

          if (accessToken && refreshToken) {
            const { data: sessionData, error: sessionError } = await supabaseClient.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });

            if (sessionError) {
              setStatus('error');
              setMessage('Email confirmation failed');
              setTimeout(() => router.push('/auth/sign-in'), 2000);
              return;
            }

            setStatus('success');
            setMessage('Email confirmed! Redirecting to dashboard...');

            // Refresh user data before redirecting
            await refreshUser();

            // Navigate to home page
            setTimeout(() => {
              router.push('/');
            }, 1500);
            return;
          }
        }

        // Handle regular OAuth callback
        const { data, error } = await supabaseClient.auth.getSession();

        if (error) {
          setStatus('error');
          setMessage(error.message || 'Authentication failed');
          
          // Redirect to sign-in with error message
          setTimeout(() => {
            router.push(`/auth/sign-in?error=${encodeURIComponent(error.message || 'Authentication failed')}`);
          }, 2000);
          return;
        }

        if (data.session) {
          // Store tokens securely
          storeAuthTokens(
            data.session.access_token,
            data.session.refresh_token,
            data.session.expires_at
          );

          // Update user record if needed
          try {
            const result = await oauthCallbackFetcher.post('/api/v1/session/oauth-callback', {
              user: data.session.user,
              provider: data.session.user.app_metadata?.provider,
            }, {
              headers: {
                'Authorization': `Bearer ${data.session.access_token}`,
              },
              showErrorToast: false, // Don't show error toast for this optional operation
            });

            if (!result.success) {
            }
          } catch (updateError) {
            // Don't fail the authentication for this
          }

          setStatus('success');
          setMessage('Authentication successful! Redirecting...');
          
          // Redirect to dashboard or intended page
          setTimeout(() => {
            const redirectTo = new URLSearchParams(window.location.search).get('redirect_to') || '/';
            router.push(redirectTo);
          }, 1500);
        } else {
          setStatus('error');
          setMessage('No session found. Please try signing in again.');
          
          setTimeout(() => {
            router.push('/auth/sign-in');
          }, 2000);
        }
      } catch (err) {
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
        
        setTimeout(() => {
          router.push('/auth/sign-in');
        }, 2000);
      }
    };

    handleAuthCallback();
  }, [router, oauthCallbackFetcher, refreshUser]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full mx-auto text-center p-6">
        <div className="mb-8">
          {status === 'loading' && (
            <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <Loader className="h-8 w-8 text-primary animate-spin" />
            </div>
          )}
          
          {status === 'success' && (
            <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
          )}
          
          {status === 'error' && (
            <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          )}
          
          <h1 className="text-2xl font-bold text-foreground mb-2">
            {status === 'loading' && 'Completing Sign In'}
            {status === 'success' && 'Sign In Successful'}
            {status === 'error' && 'Sign In Failed'}
          </h1>
          
          <p className="text-muted-foreground">{message}</p>
        </div>
        
        {status === 'loading' && (
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
            <p className="text-sm text-muted-foreground">Please wait...</p>
          </div>
        )}
      </div>
    </div>
  );
}
