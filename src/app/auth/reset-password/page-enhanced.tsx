"use client";

import React, { useState } from "react";
import { zodR<PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { ArrowLeft, Mail, AlertCircle, CheckCircle2, Loader } from "lucide-react";
import { resetPasswordSchema, type ResetPasswordFormData } from "@/lib/auth/validation";
import { usePasswordReset } from "@/lib/auth/hooks";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { FORM_INTERACTION } from "@/constants/messages";

const ResetPasswordPage = () => {
  const { handlePasswordReset, loading, error, success, setError } = usePasswordReset();

  const form = useForm<ResetPasswordFormData>({
    resolver: zod<PERSON><PERSON>olver(resetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(values: ResetPasswordFormData) {
    setError(null);
    await handlePasswordReset(values.email);
  }

  if (success) {
    return (
      <div className="max-w-md mx-auto text-center">
        <div className="mb-8">
          <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
            <CheckCircle2 className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
            Check Your Email
          </h1>
          <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
            We've sent a password reset link to <strong>{form.getValues("email")}</strong>
          </p>
        </div>

        <div className="bg-muted/50 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <Mail className="h-5 w-5 text-primary" />
            <span className="font-medium">What to do next:</span>
          </div>
          <ol className="text-sm text-secondary-foreground space-y-2 text-left">
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">1</span>
              Check your email inbox for a message from us
            </li>
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">2</span>
              Click the "Reset Password" link in the email
            </li>
            <li className="flex items-start gap-2">
              <span className="flex-shrink-0 w-5 h-5 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">3</span>
              Create a new secure password
            </li>
          </ol>
        </div>

        <div className="space-y-4">
          <p className="text-sm text-secondary-foreground">
            Didn't receive the email? Check your spam folder or{" "}
            <Button
              variant="link"
              className={`p-0 h-auto text-primary ${
                loading ? 'pointer-events-none opacity-50' : ''
              }`}
              onClick={() => handlePasswordReset(form.getValues("email"))}
              disabled={loading}
            >
              try again
            </Button>
          </p>

          <Link href="/auth/sign-in">
            <Button 
              variant="outline" 
              className={`w-full ${
                loading ? 'pointer-events-none opacity-50' : ''
              }`}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Sign In
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Mail className="h-8 w-8 text-primary" />
        </div>
        <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
          Reset Your Password
        </h1>
        <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="email"
                    placeholder="Enter your email address"
                    autoComplete="email"
                    className="h-11"
                    disabled={loading}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <Button
            disabled={loading}
            type="submit"
            className="w-full bg-green px-6 py-5 dark:text-white cursor-pointer"
          >
            {loading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Sending Reset Link...
              </>
            ) : (
              "Send Reset Link"
            )}
          </Button>
        </form>
      </Form>

      <div className="flex items-center justify-center text-sm my-6 gap-1">
        <p className="text-secondary-foreground">Remember your password?</p>
        <Link 
          href="/auth/sign-in" 
          className={`font-semibold text-primary hover:underline ${
            loading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          Sign in
        </Link>
      </div>

      {/* Security Notice */}
      <div className="bg-muted/50 rounded-lg p-4 mt-6">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle2 className="h-4 w-4 text-green-500" />
          <span className="text-sm font-medium">Security Notice</span>
        </div>
        <ul className="text-xs text-secondary-foreground space-y-1">
          <li>• Reset links expire in 24 hours for security</li>
          <li>• We'll never ask for your password via email</li>
          <li>• If you didn't request this, you can safely ignore it</li>
        </ul>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
