"use client";
import React, { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import Image from "next/image";
import { Eye, EyeIcon, EyeOff, EyeOffIcon } from "lucide-react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useAuthApi } from "@/hooks/useApi";
import { FORM_INTERACTION } from "@/constants/messages";

const formSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6).max(40),
});

const Login = () => {
  const [showPw, setShowPw] = useState(false);
  const router = useRouter();
  const { signIn } = useAuthApi();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(body: z.infer<typeof formSchema>) {
    try {
      const result = await signIn.execute(body.email, body.password);
      if (result.success) {
        toast.success("Login successful!");
        router.push("/");
        form.reset();
      }
      // Error handling is automatic via the fetcher hook
    } catch (error: any) {
      toast.error(error?.message || error);
    }
  }

  useEffect(() => {
    // Get the hash fragment from the URL (e.g., "#access_token=...&expires_at=...")
    const hash = window.location.hash;
    if (hash) {
      // Remove the '#' and parse the parameters
      const params = new URLSearchParams(hash.slice(1));
      const accessToken = params.get("access_token");
      const error = params.get("error");
      const errorCode = params.get("error_code");
      const errorDescription = params.get("error_description");
      if (accessToken) {
        // Set the token in a cookie that expires in 7 days and is available site-wide.
        Cookies.set("session", accessToken, { expires: 1 / 24, path: "/" });
        router.push("/");
        // Remove the token from the URL
        window.history.replaceState(null, "", window.location.pathname);
      }

      if (error) {
        // Decode the error description if it's URL encoded
        const decodedDescription = errorDescription ? decodeURIComponent(errorDescription) : "";
        // Display an error toast with a custom message
        toast.error(decodedDescription);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
        Welcome! login to your account
      </h1>
      <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
        Log in to manage your Dapps, view performance, and stay connected with your users.
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7 my-7">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Your email ID</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    className="h-10" 
                    placeholder="Email" 
                    disabled={signIn.loading}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Password</FormLabel>
                <FormControl className="relative">
                  <div className="relative">
                    <Input 
                      type={showPw ? "text" : "password"} 
                      className="h-10" 
                      placeholder="Password" 
                      {...field} 
                      disabled={signIn.loading}
                    />
                    {!showPw ? (
                      <Eye
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          signIn.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={signIn.loading ? undefined : () => setShowPw(true)}
                      />
                    ) : (
                      <EyeOff
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          signIn.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={signIn.loading ? undefined : () => setShowPw(false)}
                      />
                    )}
                  </div>
                </FormControl>
                <FormMessage className="text-xs" />
                <Link 
                  href={"/auth/reset-password"} 
                  className={`text-end text-sm text-green ${
                    signIn.loading ? 'pointer-events-none opacity-50' : ''
                  }`}
                >
                  Forgot password
                </Link>
              </FormItem>
            )}
          />
          <Button type="submit" className="bg-green px-6 py-5 dark:text-white cursor-pointer" disabled={signIn.loading}>
            {signIn.loading ? "Signing in..." : "Login"}
          </Button>
        </form>
      </Form>

      <div className="flex items-center justify-center text-xs my-7 gap-1">
        <p className="text-secondary-foreground">Don&apos;t have an account?</p>
        <Link 
          href={"/auth/sign-up"} 
          className={`font-semibold text-green ${
            signIn.loading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          Register here
        </Link>
      </div>
    </div>
  );
};

export default Login;
