"use client";

import ToggleTheme from "@/components/home/<USER>";
import Loader from "@/components/loading";
import Logo from "@/components/Logo";
import { globalStore } from "@/store/global";
import Image from "next/image";
import { PropsWithChildren } from "react";

const AuthLayout: React.FC<PropsWithChildren> = ({ children }) => {
  const { loading } = globalStore((state) => state);
  return (
    <div className="h-screen w-screen relative">
      <div className="fixed md:top-10 md:right-10 top-5 right-5">
        <ToggleTheme />
      </div>

      <div className="grid grid-cols-3 size-full">
        <div className="hidden lg:block col-span-1 relative">
          <Logo />
          <div className="size-full relative">
            <Image src={"/auth_bg.webp"} alt="" sizes="auto" fill className="object-cover" />
          </div>
        </div>
        <div className="col-span-3 lg:col-span-2 flex items-center justify-center">
          <div className="w-[520px] bg-card rounded-2xl px-6 mx-4 lg:px-[60px] py-[30px]">{children}</div>
        </div>
      </div>
      {loading && <Loader msg={typeof loading === "string" ? loading : ""} />}
    </div>
  );
};

export default AuthLayout;
