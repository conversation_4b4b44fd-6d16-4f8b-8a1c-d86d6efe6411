"use client";

import React, { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, Key, AlertCircle, CheckCircle2, Loader } from "lucide-react";
import { updatePasswordSchema, type UpdatePasswordFormData } from "@/lib/auth/validation";
import { useNewPassword, usePasswordValidation } from "@/lib/auth/hooks";
import { Alert, AlertDescription } from "@/components/ui/alert";

const NewPasswordPage = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [tokenError, setTokenError] = useState<string | null>(null);
  
  const { handleNewPassword, loading, error, setError } = useNewPassword();
  const { validation, updatePassword } = usePasswordValidation();

  const form = useForm<UpdatePasswordFormData>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const watchedPassword = form.watch("password");

  // Update password validation when password changes
  useEffect(() => {
    if (watchedPassword) {
      updatePassword(watchedPassword);
    }
  }, [watchedPassword, updatePassword]);

  // Extract token from URL fragment
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const params = new URLSearchParams(hash.slice(1));
      const accessToken = params.get("access_token");
      const error = params.get("error");
      const errorDescription = params.get("error_description");
      
      if (accessToken) {
        setToken(accessToken);
      } else if (error) {
        const decodedDescription = errorDescription ? decodeURIComponent(errorDescription) : "";
        setTokenError(decodedDescription || "Invalid or expired reset link");
      } else {
        setTokenError("No reset token found. Please request a new password reset link.");
      }
      
      // Clean up URL
      window.history.replaceState(null, "", window.location.pathname);
    } else {
      setTokenError("No reset token found. Please request a new password reset link.");
    }
  }, []);

  async function onSubmit(values: UpdatePasswordFormData) {
    if (!token) {
      setError("Invalid reset token. Please request a new password reset link.");
      return;
    }

    setError(null);
    
    const result = await handleNewPassword(values.password);
    
    if (result.success) {
      router.push("/auth/sign-in?message=Password updated successfully! You can now sign in with your new password.");
    }
  }

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return "text-red-500";
    if (score < 4) return "text-yellow-500";
    return "text-green-500";
  };

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return "Weak";
    if (score < 4) return "Medium";
    return "Strong";
  };

  // Show error state if no valid token
  if (tokenError) {
    return (
      <div className="max-w-md mx-auto text-center">
        <div className="mb-8">
          <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
            Invalid Reset Link
          </h1>
          <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
            {tokenError}
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={() => router.push("/auth/reset-password")}
            className="w-full bg-green px-6 py-5 dark:text-white"
          >
            Request New Reset Link
          </Button>
          
          <Button
            variant="outline"
            onClick={() => router.push("/auth/sign-in")}
            className="w-full"
          >
            Back to Sign In
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Key className="h-8 w-8 text-primary" />
        </div>
        <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
          Set New Password
        </h1>
        <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
          Create a strong password to secure your account and protect your data.
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  New Password
                </FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a strong password"
                      autoComplete="new-password"
                      className="h-11 pr-10"
                      disabled={loading}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={`absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent ${
                      loading ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={loading ? undefined : () => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <FormMessage className="text-xs" />
                
                {/* Password Strength Indicator */}
                {watchedPassword && (
                  <div className="mt-2 space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-secondary-foreground">
                        Password strength:
                      </span>
                      <span className={`text-xs font-medium ${getPasswordStrengthColor(validation.score)}`}>
                        {getPasswordStrengthText(validation.score)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          validation.score < 2
                            ? "bg-red-500"
                            : validation.score < 4
                            ? "bg-yellow-500"
                            : "bg-green-500"
                        }`}
                        style={{ width: `${(validation.score / 5) * 100}%` }}
                      />
                    </div>
                    {validation.feedback.length > 0 && (
                      <ul className="text-xs text-secondary-foreground space-y-1">
                        {validation.feedback.map((item, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-secondary-foreground rounded-full" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Confirm New Password
                </FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your new password"
                      autoComplete="new-password"
                      className="h-11 pr-10"
                      disabled={loading}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={`absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent ${
                      loading ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={loading ? undefined : () => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <Button
            disabled={loading || !validation.isValid}
            type="submit"
            className="w-full bg-green px-6 py-5 dark:text-white cursor-pointer"
          >
            {loading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Updating Password...
              </>
            ) : (
              "Update Password"
            )}
          </Button>
        </form>
      </Form>

      {/* Security Tips */}
      <div className="bg-muted/50 rounded-lg p-4 mt-6">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle2 className="h-4 w-4 text-green-500" />
          <span className="text-sm font-medium">Password Security Tips</span>
        </div>
        <ul className="text-xs text-secondary-foreground space-y-1">
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            Use a mix of letters, numbers, and symbols
          </li>
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            Make it at least 12 characters long
          </li>
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            Avoid common words and personal information
          </li>
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            Consider using a password manager
          </li>
        </ul>
      </div>
    </div>
  );
};

export default NewPasswordPage;
