"use client";
import React, { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { usePost } from "@/hooks/useFetcher";
import { Eye, EyeOff } from "lucide-react";
import { FORM_INTERACTION } from "@/constants/messages";

const formSchema = z
  .object({
    password: z.string().min(6).max(40),
    confirmPassword: z.string().min(6).max(40),
  })
  .superRefine(({ confirmPassword, password }, ctx) => {
    if (confirmPassword !== password) {
      ctx.addIssue({
        code: "custom",
        message: "The passwords did not match",
        path: ["confirmPassword"],
      });
    }
  });

const SetPassword = () => {
  const [showPw, setShowPw] = useState(false);
  const [showConfirmPw, setShowConfirmPw] = useState(false);
  const router = useRouter();
  const [token, setToken] = useState<string | null>(null);
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);
  const resetPasswordFetcher = usePost();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    // Get the hash fragment from the URL (e.g., "#access_token=...&expires_at=...")
    const hash = window.location.hash;

    if (hash) {
      // Remove the '#' and parse the parameters
      const params = new URLSearchParams(hash.slice(1));
      const accessToken = params.get("access_token");
      const error = params.get("error");
      const errorCode = params.get("error_code");
      const errorDescription = params.get("error_description");

      if (error) {
        const decodedDescription = errorDescription ? decodeURIComponent(errorDescription) : "";

        // Show user-friendly error messages
        if (error === "access_denied" && errorCode === "otp_expired") {
          toast.error("Reset link has expired. Please request a new one.");
        } else {
          toast.error(decodedDescription || "Reset link is invalid");
        }

        setIsTokenValid(false);
        // Redirect back to reset password page after a delay
        setTimeout(() => {
          router.push("/auth/reset-password");
        }, 3000);
        return;
      }

      if (accessToken) {
        setToken(accessToken);
        setIsTokenValid(true);
        // Clear the hash from URL for security
        window.history.replaceState(null, "", window.location.pathname);
      } else {
        toast.error("No access token found in reset link");
        setIsTokenValid(false);
        setTimeout(() => {
          router.push("/auth/reset-password");
        }, 3000);
      }
    } else {
      toast.error("Invalid reset link - no token found");
      setIsTokenValid(false);
      setTimeout(() => {
        router.push("/auth/reset-password");
      }, 3000);
    }
  }, [router]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!token) {
      toast.error("No valid reset token found");
      return;
    }

    // hasToken: !!token,
    // tokenLength: token?.length,
    // password: "***",
    // fetcherLoading: resetPasswordFetcher.loading,

    const result = await resetPasswordFetcher.post("/api/v1/session/reset-password", {
      password: values.password,
      token: token,
    });

    // success: result.success,
    // error: result.error,
    // data: result.data,

    if (result.success) {
      toast.success("Password updated successfully!");
      router.push("/auth/sign-in");
      form.reset();
    }
    // Error handling is automatic via the fetcher hook
  }

  // Show loading state while checking token
  if (isTokenValid === null) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-secondary-foreground">Verifying reset link...</p>
        </div>
      </div>
    );
  }

  // Show error state if token is invalid
  if (isTokenValid === false) {
    return (
      <div className="text-center">
        <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-red-500">Reset Link Issue</h1>
        <p className="text-secondary-foreground text-sm mt-2 leading-[1.5]">
          The reset link is invalid or has expired.
        </p>
        <div className="mt-4 space-y-3">
          <p className="text-xs text-muted-foreground">Reset links expire after 1 hour for security reasons.</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={() => router.push("/auth/reset-password")} className="bg-primary text-white">
              Request New Reset Link
            </Button>
            <Button variant="outline" onClick={() => router.push("/auth/sign-in")}>
              Back to Sign In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">Set new password</h1>
      <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
        Secure your account with a strong password to protect your DApp profile and data.
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7 my-7">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Password</FormLabel>
                <FormControl className="relative">
                  <div className="relative">
                    <Input 
                      type={showPw ? "text" : "password"} 
                      className="h-10" 
                      placeholder="Password" 
                      {...field} 
                      disabled={resetPasswordFetcher.loading}
                    />
                    {!showPw ? (
                      <Eye
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          resetPasswordFetcher.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={resetPasswordFetcher.loading ? undefined : () => setShowPw(true)}
                      />
                    ) : (
                      <EyeOff
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          resetPasswordFetcher.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={resetPasswordFetcher.loading ? undefined : () => setShowPw(false)}
                      />
                    )}
                  </div>
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Confirm Password</FormLabel>
                <FormControl className="relative">
                  <div className="relative">
                    <Input
                      type={showConfirmPw ? "text" : "password"}
                      className="h-10"
                      placeholder="Password"
                      {...field}
                      disabled={resetPasswordFetcher.loading}
                    />
                    {!showConfirmPw ? (
                      <Eye
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          resetPasswordFetcher.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={resetPasswordFetcher.loading ? undefined : () => setShowConfirmPw(true)}
                      />
                    ) : (
                      <EyeOff
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          resetPasswordFetcher.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={resetPasswordFetcher.loading ? undefined : () => setShowConfirmPw(false)}
                      />
                    )}
                  </div>
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="bg-green px-6 py-5 dark:text-white cursor-pointer w-full"
            disabled={resetPasswordFetcher.loading}
          >
            {resetPasswordFetcher.loading ? "Updating Password..." : "Update Password"}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default SetPassword;
