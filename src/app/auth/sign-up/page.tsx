"use client";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Eye, EyeOff, Loader } from "lucide-react";
import { useAuthApi } from "@/hooks/useApi";
import { FORM_INTERACTION } from "@/constants/messages";

const formSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6).max(30),
});

const SignUp = () => {
  const [showPw, setShowPw] = useState(false);
  const router = useRouter();
  const { signUp } = useAuthApi();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    const result = await signUp.execute(values.email, values.password);

    if (result.success) {
      router.push("/auth/sign-in");
    }
    // Error handling is automatic via the fetcher hook
  }

  return (
    <div>
      <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">Create Your Account</h1>
      <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
        Sign up to list your Dapp, track performance, and connect with active Web3 users.
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7 my-7">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Your email ID</FormLabel>
                <div className="relative">
                  <FormControl className="relative">
                    <Input 
                      {...field} 
                      placeholder="Email" 
                      disabled={signUp.loading}
                    />
                  </FormControl>
                </div>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input 
                      type={showPw ? "text" : "password"} 
                      className="h-10" 
                      placeholder="Password" 
                      {...field} 
                      disabled={signUp.loading}
                    />
                    {!showPw ? (
                      <Eye
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          signUp.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={signUp.loading ? undefined : () => setShowPw(true)}
                      />
                    ) : (
                      <EyeOff
                        className={`z-10 absolute top-1/2 -translate-y-1/2 right-4 ${
                          signUp.loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                        }`}
                        size={15}
                        onClick={signUp.loading ? undefined : () => setShowPw(false)}
                      />
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            disabled={signUp.loading}
            type="submit"
            className="bg-green px-6 py-5 dark:text-white cursor-pointer min-w-32"
          >
            {signUp.loading ? <Loader className="animate-spin" /> : "Create"}
          </Button>
        </form>
      </Form>

      {/* <div className="my-7 relative">
        <p className="bg-card absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 px-3 text-secondary-foreground">
          or
        </p>
        <Separator />
      </div>

      <Button className="hover:text-white transition-all bg-[#DDDFE0] w-full h-12 text-primary font-semibold dark:text-gray-700">
        <div className="overflow-hidden relative rounded-full size-4">
          <Image src={"/icons/google-icon-logo-svgrepo-com.svg"} alt="" sizes="auto" fill />
        </div>
        Sign in with Google
      </Button> */}

      <div className="flex items-center justify-center text-xs my-7 gap-1">
        <p className="text-secondary-foreground">Already have an account?</p>
        <Link 
          href={"/auth/sign-in"} 
          className={`font-semibold text-green ${
            signUp.loading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          Log-in
        </Link>
      </div>
    </div>
  );
};

export default SignUp;
