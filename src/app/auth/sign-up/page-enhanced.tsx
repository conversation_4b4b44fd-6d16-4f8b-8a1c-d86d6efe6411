"use client";

import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Loader, Eye, EyeOff, Shield, AlertCircle, CheckCircle2 } from "lucide-react";
import { signUpSchema, type SignUpFormData } from "@/lib/auth/validation";
import { useSignUp, usePasswordValidation } from "@/lib/auth/hooks";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { FORM_INTERACTION } from "@/constants/messages";

const SignUpPage = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { handleSignUp, loading, error, setError } = useSignUp();
  const { validation, updatePassword } = usePasswordValidation();

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      terms: false,
    },
  });

  const watchedPassword = form.watch("password");

  // Update password validation when password changes
  React.useEffect(() => {
    if (watchedPassword) {
      updatePassword(watchedPassword);
    }
  }, [watchedPassword, updatePassword]);

  async function onSubmit(values: SignUpFormData) {
    setError(null);
    
    const result = await handleSignUp(values.email, values.password);
    
    if (result.success) {
      router.push("/auth/sign-in?message=Please check your email to verify your account");
    }
  }

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return "text-red-500";
    if (score < 4) return "text-yellow-500";
    return "text-green-500";
  };

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return "Weak";
    if (score < 4) return "Medium";
    return "Strong";
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <Shield className="mx-auto h-12 w-12 text-primary mb-4" />
        <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">
          Create Your Account
        </h1>
        <p className="text-secondary-foreground text-sm font-medium mt-2 leading-[1.5]">
          Sign up to list your Dapp, track performance, and connect with active Web3 users.
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal text-secondary-foreground text-sm">
                    First Name
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="John" disabled={loading} />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal text-secondary-foreground text-sm">
                    Last Name
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Doe" disabled={loading} />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Email Address
                </FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    disabled={loading}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Password
                </FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a strong password"
                      autoComplete="new-password"
                      disabled={loading}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={`absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent ${
                      loading ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={loading ? undefined : () => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <FormMessage className="text-xs" />
                
                {/* Password Strength Indicator */}
                {watchedPassword && (
                  <div className={`mt-2 space-y-2 ${loading ? 'opacity-50' : ''}`}>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-secondary-foreground">
                        Password strength:
                      </span>
                      <span className={`text-xs font-medium ${getPasswordStrengthColor(validation.score)}`}>
                        {getPasswordStrengthText(validation.score)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          validation.score < 2
                            ? "bg-red-500"
                            : validation.score < 4
                            ? "bg-yellow-500"
                            : "bg-green-500"
                        }`}
                        style={{ width: `${(validation.score / 5) * 100}%` }}
                      />
                    </div>
                    {validation.feedback.length > 0 && (
                      <ul className="text-xs text-secondary-foreground space-y-1">
                        {validation.feedback.map((item, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-secondary-foreground rounded-full" />
                            {item}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground text-sm">
                  Confirm Password
                </FormLabel>
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your password"
                      autoComplete="new-password"
                      disabled={loading}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className={`absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent ${
                      loading ? 'cursor-not-allowed opacity-50' : ''
                    }`}
                    onClick={loading ? undefined : () => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={loading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className={`text-sm font-normal text-secondary-foreground ${
                    loading ? 'opacity-50' : ''
                  }`}>
                    I agree to the{" "}
                    <Link 
                      href="/terms" 
                      className={`text-primary hover:underline ${
                        loading ? 'pointer-events-none opacity-50' : ''
                      }`}
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link 
                      href="/privacy" 
                      className={`text-primary hover:underline ${
                        loading ? 'pointer-events-none opacity-50' : ''
                      }`}
                    >
                      Privacy Policy
                    </Link>
                  </FormLabel>
                  <FormMessage className="text-xs" />
                </div>
              </FormItem>
            )}
          />

          <Button
            disabled={loading}
            type="submit"
            className="w-full bg-green px-6 py-5 dark:text-white cursor-pointer"
          >
            {loading ? (
              <>
                <Loader className="animate-spin mr-2 h-4 w-4" />
                Creating Account...
              </>
            ) : (
              "Create Account"
            )}
          </Button>
        </form>
      </Form>

      <div className="flex items-center justify-center text-sm my-6 gap-1">
        <p className="text-secondary-foreground">Already have an account?</p>
        <Link 
          href="/auth/sign-in" 
          className={`font-semibold text-primary hover:underline ${
            loading ? 'pointer-events-none opacity-50' : ''
          }`}
        >
          Sign in
        </Link>
      </div>

      {/* Security Features */}
      <div className="bg-muted/50 rounded-lg p-4 mt-6">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">Your data is secure</span>
        </div>
        <ul className="text-xs text-secondary-foreground space-y-1">
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            End-to-end encryption
          </li>
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            Email verification required
          </li>
          <li className="flex items-center gap-2">
            <CheckCircle2 className="h-3 w-3 text-green-500" />
            No spam, unsubscribe anytime
          </li>
        </ul>
      </div>
    </div>
  );
};

export default SignUpPage;
