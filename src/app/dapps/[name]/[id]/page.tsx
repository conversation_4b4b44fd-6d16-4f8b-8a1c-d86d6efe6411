"use client";
import Feedbacks from "@/components/dapp-detail/Feedbacks";
import Grid from "@/components/dapp-detail/Grid";
import { DApp } from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import Header from "@/components/home/<USER>";
import { supabaseClient } from "@/lib/supabase/client";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const DappDetail = () => {
  const searchParams = useParams();
  const id = searchParams.id;
  const router = useRouter();

  const [dapp, setDapp] = useState<DApp | null>(null);

  useEffect(() => {
    const fetchDapp = async () => {
      const { data } = await supabaseClient.from("dapps").select("*").eq("id", id).single();
      if (!data) {
        router.push("/not-found");
        return;
      }

      setDapp(data);
    };

    fetchDapp();
  }, [id, router]);

  return (
    dapp && (
      <div>
        <Header />
        <div className="mt-10 lg:mt-20 px-4 lg:px-0">
          <Grid dapp={dapp} />
          <Feedbacks />
        </div>
        <Footer />
      </div>
    )
  );
};

export default DappDetail;
