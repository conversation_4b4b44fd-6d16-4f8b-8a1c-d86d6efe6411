import Header from "@/components/home/<USER>";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const PaymentFailed = () => {
  return (
    <div className="flex flex-col h-screen">
      <Header />
      <div className="flex-1 flex items-center justify-center">
        <div className="mx-auto lg:min-w-xl">
          <div className="mx-auto size-44 lg:size-[200px] relative">
            <Image src={"/f4d5406bea753ad3aedcef541324138adb031eba.png"} alt="" sizes="auto" fill />
          </div>
          <div className="my-4 space-y-1">
            <p className="text-center text-4xl font-protest-strike uppercase text-primary">Payment Failed!</p>
            <div className="text-center text-sm text-secondary-foreground">
              <p>
                Something went wrong and your transaction <br className="lg:hidden" /> couldn't be completed.
              </p>
              <p>Please try again or use a different payment method.</p>
            </div>
          </div>
          <Separator />
          <div className="text-sm text-secondary-foreground space-y-2 my-4">
            <div className="flex justify-between items-center">
              <p>Amount Paid:</p>
              <p>$30</p>
            </div>
            <div className="flex justify-between items-center">
              <p>Transaction ID:</p>
              <p>#TXN12345678</p>
            </div>
            <div className="flex justify-between items-center">
              <p>Payment Method:</p>
              <p>UPI / Wallet / Card</p>
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <Link href="/upload" className="flex-1">
              <Button variant={"outline"} className="h-11 border border-[#01BB6A] text-[#01BB6A] w-full">
                Try Again
              </Button>
            </Link>
            <Link href="/" className="flex-1">
              <Button variant={"outline"} className="h-11 border border-[#01BB6A] text-[#01BB6A] w-full">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailed;
