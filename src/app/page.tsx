"use client";

import About from "@/components/home/<USER>";
import Banner from "@/components/home/<USER>";
import Discovery from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import GuideLine from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import PopularDapps from "@/components/home/<USER>";
import Pricing from "@/components/home/<USER>";
import Loader from "@/components/loading";
import { globalStore } from "@/store/global";

export default function Home() {
  const { loading } = globalStore();
  
  return (
    <div>
      <Hero />
      <About />
      <Discovery />
      <PopularDapps />
      <Pricing />
      <GuideLine />
      <Banner />
      <Footer />

      {loading && <Loader msg={typeof loading === "string" ? loading : ""} />}
    </div>
  );
}
