"use client";
import Logo from "@/components/Logo";
import Image from "next/image";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

const formSchema = z.object({
  fullname: z.string().min(3).max(40),
  displayName: z.string().min(3).max(40),
});

const Welcome = () => {
  const [avatarPreview, setAvatarPreview] = useState<string>("");

  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullname: "",
      displayName: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values.
    // ✅ This will be type-safe and validated.
    router.push("/congratulations");
  }

  const handleSelectAvatar = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="grid grid-cols-3 h-screen w-screen">
      <div className="col-span-1 relative">
        <Logo />
        <div className="size-full relative">
          <Image src={"/welcome_bg.webp"} alt="" sizes="auto" fill className="object-cover" />
        </div>
      </div>
      <div className="col-span-2 flex items-center justify-center">
        <div className="w-[520px] bg-card rounded-2xl px-6 mx-4 lg:px-[60px] py-[30px]">
          <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">Welcome!</h1>
          <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
            Create your profile, verify your details, and unlock access to 169.5M+ users worldwide.
          </p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7 my-7">
              <FormItem>
                <FormLabel className="font-normal text-secondary-foreground cursor-pointer text-sm" htmlFor="avatar">
                  Avatar
                </FormLabel>
                {!avatarPreview ? (
                  <FormLabel
                    htmlFor="avatar"
                    className="size-16 border rounded-2xl cursor-pointer dark:bg-input/30 bg-input"
                  >
                    <Input type="file" className="hidden" id="avatar" accept="image/*" onChange={handleSelectAvatar} />
                  </FormLabel>
                ) : (
                  <div className="group size-16 border rounded-2xl cursor-pointer relative overflow-hidden">
                    <div
                      className="group-hover:opacity-100 transition-all opacity-0 absolute top-0 left-0 size-full flex items-center justify-center z-10 bg-black/20"
                      onClick={() => {
                        setAvatarPreview("");
                      }}
                    >
                      <Trash2 className="text-red-500" />
                    </div>
                    <Image src={avatarPreview} sizes="auto" alt="" fill className="object-cover" />
                  </div>
                )}
              </FormItem>

              <FormField
                control={form.control}
                name="fullname"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-secondary-foreground text-sm">Full name</FormLabel>
                    <FormControl>
                      <Input {...field} className="h-10" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-normal text-secondary-foreground text-sm">Display name</FormLabel>
                    <FormControl>
                      <Input {...field} className="h-10" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <Button type="submit" className="bg-green px-6 py-5 dark:text-white cursor-pointer">
                Next
              </Button>

              <div className="flex items-center justify-center text-xs gap-1">
                <p className="text-secondary-foreground">Already have an account?</p>
                <Link href={"/auth/login"} className="font-semibold text-green">
                  Log-in
                </Link>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Welcome;
