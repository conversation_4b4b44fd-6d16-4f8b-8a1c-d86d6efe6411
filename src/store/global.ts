import { create } from "zustand";
import { devtools } from "zustand/middleware";

type GlobalStore = {
  loading: boolean | string;
  loadIndex: number;
  setLoading: (loading: boolean | string) => void;
};

const useGlobalStore = create<GlobalStore>()(
  devtools(
    (set) => ({
      loading: false,
      loadIndex: 0,
      setLoading: (loading) => {
        set((state) => {
          const isStarting = loading === true || typeof loading === "string";

          const updatedIndex = isStarting
            ? state.loadIndex + 1
            : Math.max(0, state.loadIndex - 1);

          const updatedLoading =
            updatedIndex > 0
              ? isStarting
                ? loading
                : state.loading
              : false;

          return {
            loadIndex: updatedIndex,
            loading: updatedLoading,
          };
        });
      },
    }),
    { name: "GlobalStore" } // name for Redux DevTools extension
  )
);

export const globalStore = useGlobalStore;
