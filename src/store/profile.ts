import { User } from "@supabase/supabase-js";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

type ProfileStore = {
  user: User | null;
  setUser: (user: User | null) => void;
  clear: () => void;
};

export const useProfileStore = create<ProfileStore>()(
  devtools(
    (set) => ({
      user: null,
      setUser: (user) => set(() => ({ user })),
      clear: () => set(() => ({ user: null })),
    }),
    { name: "ProfileStore" } // Helps identify the store in Redux DevTools
  )
);
