"use client";

import blockchainService from "@/blockchain/blockchainService";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface WalletState {
  isInjected: boolean;
  address: string;
  isConnected: boolean;
  balance: number;
  chainId: number;
  network: string;
  isGenerated: boolean;
  usdPrice: number | null;
  walletBalance: number;
}

export const initialState: WalletState = {
  isInjected: false,
  address: "",
  isConnected: false,
  balance: 0,
  chainId: 0,
  network: "",
  isGenerated: false,
  usdPrice: null,
  walletBalance: 0,
};

export type WalletActions = {
  setIsInjected: (val: boolean) => void;
  setAddress: (address: string) => void;
  setIsConnected: (val: boolean) => void;
  setBalance: (val: number) => void;
  setChainId: (val: number) => void;
  setNetwork: (val: string) => void;
  fetchIsGenerated: (address: string) => Promise<void>;
  setIsGenerated: (val: boolean) => void;
  setInitialState: () => void;
  fetchUSDPrice: () => Promise<void>;
  fetchWalletBalance: (addr?: string) => Promise<void>;
};

export const walletStore = create<WalletState & WalletActions>()(
  devtools((set, get) => ({
    ...initialState,

    setIsInjected: (val) => set({ isInjected: val }),
    setAddress: (address) => {
      set({ address });
      void get().fetchIsGenerated(address);
    },
    setIsConnected: (val) => set({ isConnected: val }),
    setBalance: (val) => set({ balance: val }),
    setChainId: (val) => set({ chainId: val }),
    setNetwork: (val) => set({ network: val }),
    setIsGenerated: (val) => set({ isGenerated: val }),

    fetchIsGenerated: async (address) => {
      if (!address) return set({ isGenerated: false });

      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_ENKRYPTED_APP}/wallet-record?address=${address}`
        );
        const { data } = await res.json();
        set({ isGenerated: data });
      } catch (err) {
        set({ isGenerated: false });
      }
    },

    fetchUSDPrice: async () => {
      const bc = new blockchainService();
      bc.getBNRYPrice()
        .then((usdPrice) => {
          set({ usdPrice });
        })
        .catch((error) => {
        });
    },

    fetchWalletBalance: async (addr?: string) => {
      const address = addr || get().address;
      if (address) {
        const bc = new blockchainService();
        bc.getBNRYBalance(address)
          .then((balance) => {
            set({ walletBalance: Number(balance) });
          })
          .catch((error) => {
          });
      }
    },

    setInitialState: () => {
      const { isInjected } = get();
      set({ ...initialState, isInjected });
    },
  }))
);

// === Wallet Event Handlers ===

const handleUpdate = (e: CustomEvent) => {
  const { isConnected, address } = e.detail;
  walletStore.getState().setIsConnected(isConnected);
  walletStore.getState().setAddress(address || "");
};

export const updateWalletInfo = () => {
  if (typeof window !== "undefined") {
    const wallet = (window as any).enkrypted;
    const store = walletStore.getState();

    store.setIsInjected(!!wallet);

    const info = wallet?.getAccount?.();
    if (info) {
      store.setIsConnected(info.isConnected);
      store.setAddress(info.address || "");
      store.setChainId(info.chainId || 0);
      store.setNetwork(info.network || "");
      store.setBalance(info.balance || 0);
    }

    window.addEventListener("enkryptedUpdate", handleUpdate as EventListener);

    return () => {
      window.removeEventListener(
        "enkryptedUpdate",
        handleUpdate as EventListener
      );
    };
  }
};

// Call this once to initialize
updateWalletInfo();

export type WalletStore = typeof walletStore;
