import { create } from 'zustand'

export interface IGame {
  id: string
  title: string
  description: string
  category: string
  link: string
  paymentId: string
  createdBy: string
  updatedAt: string
  status: string
  thumbnail: File
  banner: File
  validTill: string
  isActive?: boolean
}

export interface GameSession {
  game_id: string
  total_session_time: number
}

type State = {
  game: IGame
  onlinePlayers: number;
  session: number
  loading: string
  playCount: number
  rating: Array<any>
  updateGame: (data: IGame) => void
  setSession: (data: number) => void
  setOnlinePlayers: (data: number) => void
  setLoading: (data: string) => void,
  setPlayCount: (data: number) => void
  setRating: (data: number) => void
  reset: () => void
}

const initialState: IGame = {
  id: '',
  title: '',
  description: '',
  category: '',
  link: '',
  paymentId: '',
  createdBy: '',
  updatedAt: '',
  status: '',
  thumbnail: new File([], ''),
  banner: new File([], ''),
  validTill: '',
}

export const singleGameStore = create<State>((set) => ({
  game: initialState,
  onlinePlayers: 0,
  session: 0,
  playCount: 0,
  rating: [],
  loading: 'Fetching game data...',
  updateGame: (game: IGame) => set((state: any) => ({ ...state, game })),
  setSession: (session: number) => set((state: any) => ({ ...state, session })),
  setOnlinePlayers: (onlinePlayers: number) => set((state: any) => ({ ...state, onlinePlayers })),
  setLoading: (loading: string) => set((state: any) => ({ ...state, loading })),
  setPlayCount: (playCount: number) => set((state: any) => ({ ...state, playCount })),
  setRating: (rating: number) => set((state: any) => ({ ...state, rating })),
  reset: () => set({ game: initialState, session: 0, loading: '' }),
}))
