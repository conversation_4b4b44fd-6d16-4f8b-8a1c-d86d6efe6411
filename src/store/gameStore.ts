import { create } from 'zustand'

export class Game {
  thumbnail?: File
  banner?: File
  title: string = ''
  description: string = ''
  category: string = ''
  link: string = ''
  paymentId: string = ''
  createdBy: string = ''
  constructor() {}
}

export const gameStore = create((set) => ({
  game: new Game(),
  step: 0,
  nextStep: () => set((state: { step: number }) => ({ step: state.step + 1 })),
  updateGame: (data: Game) =>
    set((state: any) => ({ game: { ...state.game, ...data } })),
  reset: () => set({ game: new Game(), step: 0 }),
}))
