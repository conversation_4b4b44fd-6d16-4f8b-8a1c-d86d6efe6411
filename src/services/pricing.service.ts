import { supabaseClient } from '@/lib/supabase/client'

export interface PricingPlan {
  id: number
  name: string
  price_usd: number
  billing_period: string
  features: string[]
  is_active: boolean
  stripe_price_id?: string
  created_at: string
  updated_at: string
}

/**
 * Get all active pricing plans
 */
export const getAllPricingPlans = async (): Promise<{ data: PricingPlan[] | null; error: any }> => {
  const { data, error } = await supabaseClient
    .from('pricing_plans')
    .select('*')
    .eq('is_active', true)
    .order('price_usd', { ascending: true })

  return { data, error }
}

/**
 * Get pricing plan by ID
 */
export const getPricingPlanById = async (id: number): Promise<{ data: PricingPlan | null; error: any }> => {
  const { data, error } = await supabaseClient
    .from('pricing_plans')
    .select('*')
    .eq('id', id)
    .eq('is_active', true)
    .single()

  return { data, error }
}

/**
 * Get pricing plan by name
 */
export const getPricingPlanByName = async (name: string): Promise<{ data: PricingPlan | null; error: any }> => {
  const { data, error } = await supabaseClient
    .from('pricing_plans')
    .select('*')
    .eq('name', name)
    .eq('is_active', true)
    .single()

  return { data, error }
}

/**
 * Create new pricing plan
 */
export const createPricingPlan = async (plan: Omit<PricingPlan, 'id' | 'created_at' | 'updated_at'>): Promise<{ data: PricingPlan | null; error: any }> => {
  const { data, error } = await supabaseClient
    .from('pricing_plans')
    .insert([plan])
    .select('*')
    .single()

  return { data, error }
}

/**
 * Update pricing plan
 */
export const updatePricingPlan = async (id: number, updates: Partial<PricingPlan>): Promise<{ data: PricingPlan | null; error: any }> => {
  const { data, error } = await supabaseClient
    .from('pricing_plans')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select('*')
    .single()

  return { data, error }
}

/**
 * Helper function to convert USD to cents for Stripe
 */
export const usdToCents = (usd: number): number => {
  return Math.round(usd * 100)
}
