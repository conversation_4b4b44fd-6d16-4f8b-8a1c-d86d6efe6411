import { walletStore } from "../store/wallet.store";

export const connect = () => {
  if (typeof window !== "undefined") {
    if (
      window.enkrypted &&
      typeof window.enkrypted.connect === "function"
    ) {
      window.enkrypted.connect();
    } else {
      // Binary wallet is not available or the connect function is missing.
    }
  }
};

export const disconnect = () => {
  if (typeof window !== "undefined") {
    if (
      window.enkrypted &&
      window.enkrypted?.isConnected &&
      typeof window.enkrypted.disconnect === "function"
    ) {
      window.enkrypted.disconnect().then(d => {
        walletStore.getState().setInitialState();
      })
    } else {
      // Binary wallet is not available or the connect function is missing.
    }
  }
}
