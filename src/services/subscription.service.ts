import { supabaseClient } from '@/lib/supabase/client'

export type Subscription = {
  gameId: string
  paymentId: string
  validTill: Date
}

export const addSubscription = async (params: Subscription) => {
  const { gameId, paymentId, validTill } = params

  // Since there's no subscriptions table in the current schema,
  // we'll update the dapps table with subscription info instead
  try {

    const { data, error } = await supabaseClient
      .from('dapps')
      .update({
        paymentid: paymentId, // lowercase to match schema
        validtill: validTill, // lowercase to match schema
      })
      .eq('id', gameId)
      .select('*')
      .single()

    if (error) {
      return { data: null, error }
    }

    return { data, error: null }

  } catch (err) {
    return { data: null, error: err }
  }
}
