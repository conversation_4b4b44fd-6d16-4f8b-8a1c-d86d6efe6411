import { ethers } from "ethers"

export const BNRY_ADDRESS = {
    mainnet : '******************************************',
    testnet : '******************************************'
  }

  export const conversions = {
    toWei: (amount: number, decimals = 18): bigint => {
      return ethers.parseUnits(String(amount), decimals)
    },
    toEther: (amount: string, decimals = 18): string => {
      return ethers.formatUnits(amount, decimals).toString()
    },
  }
  
  export const RECEIVER_ADDRESS = {
    mainnet: '******************************************',
    testnet: '******************************************',
  }

  export const USDT_ADDRESS = {
    mainnet : '******************************************',
    testnet : '******************************************'
  }

  export const USDT_DECIMALS = 6;

  export const BNRY_RPC_URL = {
    "mainnet": "https://rpc-binaryholdings.cogitus.io/ext/bc/J3MYb3rDARLmB7FrRybinyjKqVTqmerbCr9bAXDatrSaHiLxQ/rpc",
    "testnet": "https://rpc-binaryholding-testnet.cogitus.io/2LjflCs6YFUdfV0QZ9W/ext/bc/tJGE3Wrw2aCiR8AyEEWVCKhYHKcW4oWCU9N1tbXWjTisDDskK/rpc"
}