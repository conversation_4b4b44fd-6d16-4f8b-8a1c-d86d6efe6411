import { FILE_UPLOAD_CONFIG } from '@/constants/app';
import { SUPABASE_ENDPOINTS } from '@/constants/supabase';

/**
 * Validates if a logo value is a valid image URL
 * @param logo - The logo value to validate
 * @returns boolean - True if valid image URL, false otherwise
 */
export function isValidImageUrl(logo: any): logo is string {
  // Check if logo exists and is a string
  if (!logo || typeof logo !== 'string') {
    return false;
  }

  // Check if it's not an empty string or object string
  if (logo.trim() === '' || logo === '{}' || logo === 'null' || logo === 'undefined') {
    return false;
  }

  // Check if it's a valid URL format
  try {
    const url = new URL(logo);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    // If it's a relative path, check if it starts with /
    return logo.startsWith('/');
  }
}

/**
 * Gets a safe image source or returns null for fallback
 * @param logo - The logo value to validate
 * @returns string | null - Valid image URL or null for fallback
 */
export function getSafeImageSrc(logo: any): string | null {
  if (!isValidImageUrl(logo)) return null;
  
  // Fix storage URL issues (double bucket names, domain mismatches)
  return fixStorageUrl(logo);
}

/**
 * Get safe image source with fallback for display
 */
export function getSafeImageSrcWithFallback(src: string, fallback?: string): string {
  if (!src || typeof src !== 'string') {
    return fallback || '/placeholder-image.png';
  }
  
  // Check if it's already a valid HTTP URL
  try {
    new URL(src);
    return src;
  } catch {
    // If not a valid URL, treat as relative path
    return src.startsWith('/') ? src : `/${src}`;
  }
}

/**
 * Generate a safe filename for uploads
 * Removes special characters and spaces, replaces with underscores
 */
export const generateSafeFilename = (dappName: string): string => {
  // Get current timestamp
  const timestamp = Date.now();
  
  // Clean the dapp name: remove spaces, special chars, and limit length
  const cleanName = dappName
    .replace(/[^a-zA-Z0-9]/g, '_') // Replace special chars with underscore
    .substring(0, 20); // Limit to 20 characters
  
  // Format: cleanName_timestamp_Image
  return `${cleanName}_Image-${timestamp}.png`;
};

/**
 * Validate if URL is a proper HTTP/HTTPS URL
 */
export function isValidHttpUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Creates a proper storage URL by avoiding double slashes
 */
export const createStorageUrl = (baseUrl: string, path: string): string => {
  if (!baseUrl || !path) return '';
  
  // Simple approach: just join and clean up all double slashes
  const url = `${baseUrl}/${path}`;
  
  // Replace all multiple slashes with single slash, except after protocol
  return url.replace(/([^:]\/)\/+/g, '$1');
};

/**
 * Gets the storage base URL with fallback
 */
export const getStorageBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_BUCKET_BASE_URL || 
         SUPABASE_ENDPOINTS.STORAGE.PUBLIC || 
         "https://jnlsblamzktjowwtmvly.supabase.co/storage/v1/object/public";
};

/**
 * Fixes URL with domain mismatch and normalizes double slashes
 * This handles cases where the storage URL has wrong domain or double slashes
 */
export const fixStorageUrl = (url: string): string => {
  if (!url) return '';
  
  // Get current environment's correct domain
  const currentSupabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  
  if (!currentSupabaseUrl) return url;
  
  // Extract the current domain
  const currentDomain = currentSupabaseUrl.replace('https://', '').replace('http://', '');
  
  // Pattern to match any Supabase storage URL
  const storageUrlPattern = /https?:\/\/([^\/]+\.supabase\.co)\/storage\/v1\/object\/public\/(.*)/;
  const match = url.match(storageUrlPattern);
  
  if (match) {
    const [, wrongDomain, pathPart] = match;
    
    // Replace with correct domain
    let correctedUrl = `https://${currentDomain}/storage/v1/object/public/${pathPart}`;
    
    // Fix double bucket name (e.g., dapp-logos/dapp-logos/ -> dapp-logos/)
    correctedUrl = correctedUrl.replace('/dapp-logos/dapp-logos/', '/dapp-logos/');
    
    // Fix double slashes (except after protocol)
    return correctedUrl.replace(/([^:]\/)\/+/g, '$1');
  }
  
  // If it's not a storage URL, just fix double slashes and bucket names
  let fixedUrl = url.replace(/([^:]\/)\/+/g, '$1');
  fixedUrl = fixedUrl.replace('/dapp-logos/dapp-logos/', '/dapp-logos/');
  
  return fixedUrl;
};
