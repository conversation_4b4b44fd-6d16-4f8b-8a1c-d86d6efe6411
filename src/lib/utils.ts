import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const openDapp = (dapp: any, owUrl: string) => {
  debugger;
  const dappData = {
    type: "dapp-click",
    dappId: dapp.id,
    dappName: dapp.name,
    dappDesc: dapp.description,
    thumbnail: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${dapp?.logo}`,
    url: dapp.live_url,
    category: dapp.category,
    banner: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${dapp?.logo}`,
  };
  window.parent.postMessage(dappData, owUrl);
};

export function isInIframe(): boolean {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
}
