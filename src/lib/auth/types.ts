import { User } from "@supabase/supabase-js";

export interface AuthUser {
  id: string;
  email?: string;
  phone?: string;
  created_at: string;
  updated_at?: string;
  email_confirmed_at?: string;
  phone_confirmed_at?: string;
  last_sign_in_at?: string;
  app_metadata: Record<string, any>;
  user_metadata?: {
    full_name?: string;
    firstName?: string;
    first_name?: string;
    lastName?: string;
    last_name?: string;
    avatar_url?: string;
    provider?: string;
    [key: string]: any; 
  };
  aud: string;
  confirmation_sent_at?: string;
  recovery_sent_at?: string;
  email_change_sent_at?: string;
  new_email?: string;
  invited_at?: string;
  action_link?: string;
  email_change?: string;
  email_change_confirm_status?: number;
  banned_until?: string;
  role?: string;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: AuthUser;
}

export interface AuthContextType {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<{ data?: any; error?: any }>;
  signIn: (email: string, password: string) => Promise<{ data?: any; error?: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ data?: any; error?: any }>;
  updatePassword: (password: string, newPassword: string) => Promise<{ data?: any; error?: any }>;
  resendConfirmation: (email: string) => Promise<{ data?: any; error?: any }>;
  refreshSession: () => Promise<void>;
}

export interface SignUpData {
  email: string;
  password: string;
  confirmPassword?: string;
  firstName?: string;
  lastName?: string;
}

export interface SignInData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface ResetPasswordData {
  email: string;
}

export interface UpdatePasswordData {
  password: string;
  confirmPassword: string;
}

export interface AuthError {
  message: string;
  status?: number;
  code?: string;
}

export type AuthProvider = 'google' | 'github' | 'discord' | 'email';

export interface AuthResponse<T = any> {
  data?: T;
  error?: AuthError;
}
