import { useState, useCallback } from "react";
import { useAuth } from "./context";
import { formatAuthError, validatePasswordStrength, RateLimiter } from "./utils";
import { toast } from "sonner";

// Rate limiter instances
const signInRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
const signUpRateLimiter = new RateLimiter(3, 60 * 60 * 1000); // 3 attempts per hour

/**
 * Hook for handling sign-up with enhanced validation and rate limiting
 */
export function useSignUp() {
  const { signUp } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSignUp = useCallback(async (email: string, password: string) => {
    // Rate limiting check
    const clientId = `signup_${email}`;
    if (!signUpRateLimiter.isAllowed(clientId)) {
      const remainingTime = Math.ceil(signUpRateLimiter.getRemainingTime(clientId) / 1000 / 60);
      setError(`Too many signup attempts. Please wait ${remainingTime} minutes before trying again.`);
      return { success: false };
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await signUp(email, password);
      
      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      // Reset rate limiter on successful signup
      signUpRateLimiter.reset(clientId);
      toast.success("Account created successfully! Please check your email to verify your account.");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [signUp]);

  return { handleSignUp, loading, error, setError };
}

/**
 * Hook for handling sign-in with rate limiting
 */
export function useSignIn() {
  const { signIn } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSignIn = useCallback(async (email: string, password: string) => {
    // Rate limiting check
    const clientId = `signin_${email}`;
    if (!signInRateLimiter.isAllowed(clientId)) {
      const remainingTime = Math.ceil(signInRateLimiter.getRemainingTime(clientId) / 1000 / 60);
      setError(`Too many login attempts. Please wait ${remainingTime} minutes before trying again.`);
      return { success: false };
    }

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await signIn(email, password);
      
      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      // Reset rate limiter on successful signin
      signInRateLimiter.reset(clientId);
      toast.success("Welcome back!");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [signIn]);

  return { handleSignIn, loading, error, setError };
}

/**
 * Hook for handling password reset
 */
export function usePasswordReset() {
  const { resetPassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handlePasswordReset = useCallback(async (email: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const { data, error } = await resetPassword(email);
      
      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      setSuccess(true);
      toast.success("Password reset link sent to your email!");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [resetPassword]);

  return { handlePasswordReset, loading, error, success, setError };
}

/**
 * Hook for handling password updates (requires current password)
 */
export function usePasswordUpdate() {
  const { updatePassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePasswordUpdate = useCallback(async (currentPassword: string, newPassword: string) => {
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await updatePassword(currentPassword, newPassword);

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      toast.success("Password updated successfully!");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [updatePassword]);

  return { handlePasswordUpdate, loading, error, setError };
}

/**
 * Hook for handling new password setting (with token, no current password required)
 */
export function useNewPassword() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleNewPassword = useCallback(async (newPassword: string) => {
    setLoading(true);
    setError(null);

    try {
      // For password reset, we use Supabase's updateUser method with the session token
      const { supabaseClient } = await import("../supabase/client");

      const { data, error } = await supabaseClient.auth.updateUser({
        password: newPassword
      });

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      toast.success("Password updated successfully!");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  return { handleNewPassword, loading, error, setError };
}

/**
 * Hook for password strength validation
 */
export function usePasswordValidation() {
  const [password, setPassword] = useState("");
  const [validation, setValidation] = useState({
    score: 0,
    feedback: [] as string[],
    isValid: false,
  });

  const updatePassword = useCallback((newPassword: string) => {
    setPassword(newPassword);
    setValidation(validatePasswordStrength(newPassword));
  }, []);

  return {
    password,
    validation,
    updatePassword,
    setPassword,
  };
}

/**
 * Hook for handling email confirmation resend
 */
export function useEmailConfirmation() {
  const { resendConfirmation } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleResendConfirmation = useCallback(async (email: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const { data, error } = await resendConfirmation(email);
      
      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      setSuccess(true);
      toast.success("Confirmation email sent!");
      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [resendConfirmation]);

  return { handleResendConfirmation, loading, error, success, setError };
}

/**
 * Hook for social authentication
 */
export function useSocialAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const signInWithProvider = useCallback(async (provider: 'google' | 'github' | 'discord') => {
    setLoading(true);
    setError(null);

    try {
      // Import supabase client dynamically to avoid SSR issues
      const { supabaseClient } = await import("../supabase/client");
      
      const { data, error } = await supabaseClient.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (err) {
      const error = formatAuthError(err);
      setError(error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  return { signInWithProvider, loading, error, setError };
}

/**
 * Hook for session management
 */
export function useSessionManagement() {
  const { user, session, refreshSession, signOut } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  const handleRefreshSession = useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshSession();
      toast.success("Session refreshed");
    } catch (error) {
      toast.error("Failed to refresh session");
    } finally {
      setRefreshing(false);
    }
  }, [refreshSession]);

  const isSessionExpiring = useCallback(() => {
    if (!session?.expires_at) return false;
    
    const expiryTime = session.expires_at * 1000;
    const currentTime = Date.now();
    const timeUntilExpiry = expiryTime - currentTime;
    
    // Return true if session expires in less than 5 minutes
    return timeUntilExpiry < 5 * 60 * 1000;
  }, [session]);

  return {
    user,
    session,
    refreshing,
    handleRefreshSession,
    isSessionExpiring,
    signOut,
  };
}

/**
 * Hook for form state management with validation
 */
export function useAuthForm<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: any
) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});

  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const setFieldTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }));
  }, []);

  const validate = useCallback(() => {
    if (!validationSchema) return true;

    try {
      validationSchema.parse(values);
      setErrors({});
      return true;
    } catch (error: any) {
      const newErrors: Partial<Record<keyof T, string>> = {};
      
      if (error.errors) {
        error.errors.forEach((err: any) => {
          if (err.path?.[0]) {
            newErrors[err.path[0] as keyof T] = err.message;
          }
        });
      }
      
      setErrors(newErrors);
      return false;
    }
  }, [values, validationSchema]);

  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validate,
    reset,
    isValid: Object.keys(errors).length === 0,
  };
}
