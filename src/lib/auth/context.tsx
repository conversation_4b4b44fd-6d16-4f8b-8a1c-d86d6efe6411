"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { AuthContextType, AuthUser, AuthSession } from "./types";
import { formatAuthError, storeAuthTokens, clearAuthTokens, getStoredTokens, isSessionExpired } from "./utils";
import { toast } from "sonner";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from server
  useEffect(() => {
    let mounted = true;

    async function initializeAuth() {
      try {
        // Check stored tokens first
        const { accessToken } = getStoredTokens();
        
        if (accessToken) {
          // Validate session with server
          const response = await fetch("/api/v1/session/session", {
            headers: {
              "Authorization": `Bearer ${accessToken}`,
            },
          });
          
          if (response.ok) {
            const result = await response.json();
            if (result.data && mounted) {
              setUser(result.data.user);
              setSession(result.data.session);
            }
          } else {
            // Session invalid, clear tokens
            clearAuthTokens();
          }
        }
      } catch (error) {
        clearAuthTokens();
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    }

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, []);

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/v1/session/sign-up", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.error) {
        const error = formatAuthError(result.error || result.message);
        return { error };
      }
      
      return { data: result.data };
    } catch (error) {
      return { error: formatAuthError(error) };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/v1/session/sign-in", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.error) {
        const error = formatAuthError(result.error || result.message);
        return { error };
      }
      
      // Extract tokens from result and store
      if (result.data?.session) {
        const sessionData = result.data.session;
        setUser(result.data.user);
        setSession(sessionData);
        
        storeAuthTokens(
          sessionData.access_token,
          sessionData.refresh_token,
          sessionData.expires_at
        );
      }
      
      return { data: result.data };
    } catch (error) {
      return { error: formatAuthError(error) };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      
      const { accessToken } = getStoredTokens();
      
      await fetch("/api/v1/session/sign-out", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(accessToken && { "Authorization": `Bearer ${accessToken}` }),
        },
      });
      
      // Clear local state and tokens
      setUser(null);
      setSession(null);
      clearAuthTokens();
      
      toast.success("Signed out successfully");
    } catch (error) {
      toast.error("Error signing out");
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const response = await fetch("/api/v1/session/reset-link", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.error) {
        const error = formatAuthError(result.error || result.message);
        return { error };
      }
      
      return { data: result.data };
    } catch (error) {
      return { error: formatAuthError(error) };
    }
  };

  const updatePassword = async (password: string, newPassword: string) => {
    try {
      const { accessToken } = getStoredTokens();
      
      if (!accessToken) {
        return { error: { message: "No active session found" } };
      }
      
      const response = await fetch("/api/v1/session/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ password, newPassword }),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.error) {
        const error = formatAuthError(result.error || result.message);
        return { error };
      }
      
      return { data: result.data };
    } catch (error) {
      return { error: formatAuthError(error) };
    }
  };

  const resendConfirmation = async (email: string) => {
    try {
      const response = await fetch("/api/v1/session/resend-confirmation", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.error) {
        const error = formatAuthError(result.error || result.message);
        return { error };
      }
      
      return { data: result.data };
    } catch (error) {
      return { error: formatAuthError(error) };
    }
  };

  const refreshSession = async () => {
    try {
      const { refreshToken } = getStoredTokens();
      
      if (!refreshToken) {
        throw new Error("No refresh token available");
      }
      
      const response = await fetch("/api/v1/session/refresh", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to refresh session");
      }
      
      const result = await response.json();
      
      if (result.data?.session) {
        const sessionData = result.data.session;
        setSession(sessionData);
        setUser(sessionData.user);
        
        storeAuthTokens(
          sessionData.access_token,
          sessionData.refresh_token,
          sessionData.expires_at
        );
      }
    } catch (error) {
      clearAuthTokens();
      setUser(null);
      setSession(null);
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    resendConfirmation,
    refreshSession,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Higher-order component for protected routes
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
): React.ComponentType<P> {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth();
    
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      );
    }
    
    if (!user) {
      // Redirect to sign in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
      return null;
    }
    
    return <WrappedComponent {...props} />;
  };
}

// Hook for protected routes
export function useRequireAuth() {
  const { user, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/auth/sign-in';
    }
  }, [user, loading]);
  
  return { user, loading };
}
