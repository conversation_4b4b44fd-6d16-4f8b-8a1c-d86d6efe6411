import type {
  AuthUser,
  Auth<PERSON>ession,
  AuthContextType,
  SignUpData,
  SignInData,
  ResetPasswordData,
  UpdatePasswordData,
  AuthError,
  AuthProvider,
  AuthResponse,
} from '../types'

describe('Auth Types', () => {
  describe('AuthUser', () => {
    it('should define required user properties', () => {
      const user: AuthUser = {
        id: '1',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        aud: 'authenticated',
        app_metadata: {},
      }

      expect(user.id).toBe('1')
      expect(user.email).toBe('<EMAIL>')
      expect(user.created_at).toBe('2023-01-01T00:00:00Z')
      expect(user.aud).toBe('authenticated')
      expect(user.app_metadata).toEqual({})
    })

    it('should allow optional user properties', () => {
      const user: AuthUser = {
        id: '1',
        created_at: '2023-01-01T00:00:00Z',
        aud: 'authenticated',
        app_metadata: {},
        phone: '+**********',
        updated_at: '2023-01-02T00:00:00Z',
        email_confirmed_at: '2023-01-01T00:01:00Z',
        phone_confirmed_at: '2023-01-01T00:02:00Z',
        last_sign_in_at: '2023-01-01T10:00:00Z',
        user_metadata: {
          full_name: 'John Doe',
          firstName: 'John',
          first_name: 'John',
          lastName: 'Doe',
          last_name: 'Doe',
          avatar_url: 'https://example.com/avatar.jpg',
          provider: 'google',
          customField: 'custom value',
        },
        confirmation_sent_at: '2023-01-01T00:00:01Z',
        recovery_sent_at: '2023-01-01T00:00:02Z',
        email_change_sent_at: '2023-01-01T00:00:03Z',
        new_email: '<EMAIL>',
        invited_at: '2023-01-01T00:00:04Z',
        action_link: 'https://example.com/action',
        email_change: '<EMAIL>',
        email_change_confirm_status: 1,
        banned_until: '2023-01-02T00:00:00Z',
        role: 'user',
      }

      expect(user.phone).toBe('+**********')
      expect(user.user_metadata?.full_name).toBe('John Doe')
      expect(user.user_metadata?.customField).toBe('custom value')
      expect(user.role).toBe('user')
    })

    it('should allow flexible user_metadata structure', () => {
      const user: AuthUser = {
        id: '1',
        created_at: '2023-01-01T00:00:00Z',
        aud: 'authenticated',
        app_metadata: {},
        user_metadata: {
          // Should accept any additional fields
          subscription: 'premium',
          preferences: {
            theme: 'dark',
            notifications: true,
          },
          socialLinks: ['twitter.com/user', 'github.com/user'],
        },
      }

      expect(user.user_metadata?.subscription).toBe('premium')
      expect(user.user_metadata?.preferences.theme).toBe('dark')
      expect(user.user_metadata?.socialLinks).toHaveLength(2)
    })
  })

  describe('AuthSession', () => {
    it('should define required session properties', () => {
      const session: AuthSession = {
        access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test',
        refresh_token: 'refresh_token_123',
        expires_at: 1672531200,
        user: {
          id: '1',
          email: '<EMAIL>',
          created_at: '2023-01-01T00:00:00Z',
          aud: 'authenticated',
          app_metadata: {},
        },
      }

      expect(session.access_token).toContain('eyJ')
      expect(session.refresh_token).toBe('refresh_token_123')
      expect(session.expires_at).toBe(1672531200)
      expect(session.user.id).toBe('1')
    })
  })

  describe('AuthContextType', () => {
    it('should define all required context methods and properties', () => {
      // This is more of a compile-time check, but we can verify the structure
      const mockContext: AuthContextType = {
        user: null,
        session: null,
        loading: false,
        signUp: async () => ({ data: null, error: null }),
        signIn: async () => ({ data: null, error: null }),
        signOut: async () => {},
        resetPassword: async () => ({ data: null, error: null }),
        updatePassword: async () => ({ data: null, error: null }),
        resendConfirmation: async () => ({ data: null, error: null }),
        refreshSession: async () => {},
      }

      expect(typeof mockContext.signUp).toBe('function')
      expect(typeof mockContext.signIn).toBe('function')
      expect(typeof mockContext.signOut).toBe('function')
      expect(typeof mockContext.resetPassword).toBe('function')
      expect(typeof mockContext.updatePassword).toBe('function')
      expect(typeof mockContext.resendConfirmation).toBe('function')
      expect(typeof mockContext.refreshSession).toBe('function')
      expect(mockContext.loading).toBe(false)
    })
  })

  describe('Form Data Types', () => {
    it('should define SignUpData structure', () => {
      const signUpData: SignUpData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!',
        firstName: 'John',
        lastName: 'Doe',
      }

      expect(signUpData.email).toBe('<EMAIL>')
      expect(signUpData.password).toBe('SecurePass123!')
      expect(signUpData.confirmPassword).toBe('SecurePass123!')
      expect(signUpData.firstName).toBe('John')
      expect(signUpData.lastName).toBe('Doe')
    })

    it('should allow optional fields in SignUpData', () => {
      const minimalSignUpData: SignUpData = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
      }

      expect(minimalSignUpData.email).toBe('<EMAIL>')
      expect(minimalSignUpData.password).toBe('SecurePass123!')
      expect(minimalSignUpData.firstName).toBeUndefined()
      expect(minimalSignUpData.lastName).toBeUndefined()
      expect(minimalSignUpData.confirmPassword).toBeUndefined()
    })

    it('should define SignInData structure', () => {
      const signInData: SignInData = {
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true,
      }

      expect(signInData.email).toBe('<EMAIL>')
      expect(signInData.password).toBe('password123')
      expect(signInData.rememberMe).toBe(true)
    })

    it('should define ResetPasswordData structure', () => {
      const resetData: ResetPasswordData = {
        email: '<EMAIL>',
      }

      expect(resetData.email).toBe('<EMAIL>')
    })

    it('should define UpdatePasswordData structure', () => {
      const updateData: UpdatePasswordData = {
        password: 'NewSecurePass123!',
        confirmPassword: 'NewSecurePass123!',
      }

      expect(updateData.password).toBe('NewSecurePass123!')
      expect(updateData.confirmPassword).toBe('NewSecurePass123!')
    })
  })

  describe('Error Types', () => {
    it('should define AuthError structure', () => {
      const error: AuthError = {
        message: 'Authentication failed',
        status: 401,
        code: 'AUTH_ERROR',
      }

      expect(error.message).toBe('Authentication failed')
      expect(error.status).toBe(401)
      expect(error.code).toBe('AUTH_ERROR')
    })

    it('should allow minimal AuthError with only message', () => {
      const error: AuthError = {
        message: 'Simple error',
      }

      expect(error.message).toBe('Simple error')
      expect(error.status).toBeUndefined()
      expect(error.code).toBeUndefined()
    })
  })

  describe('Provider Types', () => {
    it('should define AuthProvider union type', () => {
      const providers: AuthProvider[] = ['google', 'github', 'discord', 'email']

      expect(providers).toContain('google')
      expect(providers).toContain('github')
      expect(providers).toContain('discord')
      expect(providers).toContain('email')
    })

    // TypeScript compile-time check - these should not cause errors
    it('should enforce AuthProvider values at compile time', () => {
      const validProvider: AuthProvider = 'google'
      expect(validProvider).toBe('google')

      // The following would cause TypeScript errors if uncommented:
      // const invalidProvider: AuthProvider = 'facebook' // Type error
      // const invalidProvider2: AuthProvider = 'twitter' // Type error
    })
  })

  describe('Response Types', () => {
    it('should define AuthResponse for successful responses', () => {
      const successResponse: AuthResponse<{ user: AuthUser }> = {
        data: {
          user: {
            id: '1',
            email: '<EMAIL>',
            created_at: '2023-01-01T00:00:00Z',
            aud: 'authenticated',
            app_metadata: {},
          },
        },
        error: undefined,
      }

      expect(successResponse.data?.user.id).toBe('1')
      expect(successResponse.error).toBeUndefined()
    })

    it('should define AuthResponse for error responses', () => {
      const errorResponse: AuthResponse = {
        data: undefined,
        error: {
          message: 'Authentication failed',
          status: 401,
          code: 'AUTH_ERROR',
        },
      }

      expect(errorResponse.data).toBeUndefined()
      expect(errorResponse.error?.message).toBe('Authentication failed')
      expect(errorResponse.error?.status).toBe(401)
    })

    it('should allow generic AuthResponse types', () => {
      const stringResponse: AuthResponse<string> = {
        data: 'Success message',
        error: undefined,
      }

      const numberResponse: AuthResponse<number> = {
        data: 42,
        error: undefined,
      }

      const objectResponse: AuthResponse<{ id: string; name: string }> = {
        data: { id: '1', name: 'Test' },
        error: undefined,
      }

      expect(stringResponse.data).toBe('Success message')
      expect(numberResponse.data).toBe(42)
      expect(objectResponse.data?.id).toBe('1')
      expect(objectResponse.data?.name).toBe('Test')
    })
  })

  describe('Type Compatibility', () => {
    it('should allow AuthUser to be compatible with Supabase User', () => {
      // This test ensures our AuthUser type is compatible with Supabase's User type
      // In a real scenario, you'd import from @supabase/supabase-js
      const supabaseUser = {
        id: '1',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        aud: 'authenticated',
        app_metadata: {},
        user_metadata: {},
        // Additional Supabase-specific fields would be here
      }

      // Should be assignable to AuthUser
      const authUser: AuthUser = supabaseUser

      expect(authUser.id).toBe('1')
      expect(authUser.email).toBe('<EMAIL>')
    })

    it('should handle null and undefined values correctly', () => {
      const contextWithNulls: AuthContextType = {
        user: null,
        session: null,
        loading: false,
        signUp: async () => ({ data: null, error: null }),
        signIn: async () => ({ data: null, error: null }),
        signOut: async () => {},
        resetPassword: async () => ({ data: null, error: null }),
        updatePassword: async () => ({ data: null, error: null }),
        resendConfirmation: async () => ({ data: null, error: null }),
        refreshSession: async () => {},
      }

      expect(contextWithNulls.user).toBeNull()
      expect(contextWithNulls.session).toBeNull()
    })
  })
}) 