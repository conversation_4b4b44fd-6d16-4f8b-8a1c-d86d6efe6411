import React from 'react'
import { render, renderHook, act, waitFor } from '@testing-library/react'
import { AuthProvider, useAuth } from '../context'

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    signUp: jest.fn(),
    signInWithPassword: jest.fn(),
    signOut: jest.fn(),
    resetPasswordForEmail: jest.fn(),
    updateUser: jest.fn(),
    resend: jest.fn(),
    refreshSession: jest.fn(),
    getSession: jest.fn(),
    getUser: jest.fn(),
    onAuthStateChange: jest.fn(),
  },
}

jest.mock('../../supabase/client', () => ({
  supabaseClient: mockSupabaseClient,
}))

// Mock Next.js router
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  pathname: '/',
}

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}))

// Mock js-cookie
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <AuthProvider>{children}</AuthProvider>
}

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock initial session as null
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: { session: null },
      error: null,
    })
    mockSupabaseClient.auth.onAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    })
  })

  describe('AuthProvider', () => {
    it('should provide auth context', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: TestWrapper,
      })

      expect(result.current).toBeDefined()
      expect(result.current.user).toBe(null)
      expect(result.current.session).toBe(null)
      expect(result.current.loading).toBe(true) // Initially loading
      expect(typeof result.current.signUp).toBe('function')
      expect(typeof result.current.signIn).toBe('function')
      expect(typeof result.current.signOut).toBe('function')
      expect(typeof result.current.resetPassword).toBe('function')
      expect(typeof result.current.updatePassword).toBe('function')
      expect(typeof result.current.resendConfirmation).toBe('function')
      expect(typeof result.current.refreshSession).toBe('function')
    })

    it('should initialize with session from Supabase', async () => {
      const mockSession = {
        access_token: 'token',
        refresh_token: 'refresh',
        expires_at: Date.now() / 1000 + 3600,
        user: {
          id: '1',
          email: '<EMAIL>',
          created_at: '2023-01-01',
          aud: 'authenticated',
          app_metadata: {},
        },
      }

      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: TestWrapper,
      })

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.session).toEqual(mockSession)
      expect(result.current.user).toEqual(mockSession.user)
    })

    it('should handle auth state changes', async () => {
      let authStateCallback: any

      mockSupabaseClient.auth.onAuthStateChange.mockImplementation((callback) => {
        authStateCallback = callback
        return {
          data: { subscription: { unsubscribe: jest.fn() } },
        }
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: TestWrapper,
      })

      // Simulate auth state change
      const mockSession = {
        access_token: 'token',
        refresh_token: 'refresh',
        expires_at: Date.now() / 1000 + 3600,
        user: {
          id: '1',
          email: '<EMAIL>',
          created_at: '2023-01-01',
          aud: 'authenticated',
          app_metadata: {},
        },
      }

      act(() => {
        authStateCallback('SIGNED_IN', mockSession)
      })

      expect(result.current.session).toEqual(mockSession)
      expect(result.current.user).toEqual(mockSession.user)

      // Simulate sign out
      act(() => {
        authStateCallback('SIGNED_OUT', null)
      })

      expect(result.current.session).toBe(null)
      expect(result.current.user).toBe(null)
    })
  })

  describe('Auth Methods', () => {
    describe('signUp', () => {
      it('should handle successful sign up', async () => {
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          created_at: '2023-01-01',
          aud: 'authenticated',
          app_metadata: {},
        }

        mockSupabaseClient.auth.signUp.mockResolvedValue({
          data: { user: mockUser },
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.signUp('<EMAIL>', 'password123')
        })

        expect(mockSupabaseClient.auth.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
        expect(response.data.user).toEqual(mockUser)
        expect(response.error).toBe(null)
      })

      it('should handle sign up errors', async () => {
        const mockError = { message: 'Email already exists' }

        mockSupabaseClient.auth.signUp.mockResolvedValue({
          data: null,
          error: mockError,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.signUp('<EMAIL>', 'password123')
        })

        expect(response.error).toEqual(mockError)
        expect(response.data).toBe(null)
      })
    })

    describe('signIn', () => {
      it('should handle successful sign in', async () => {
        const mockSession = {
          access_token: 'token',
          refresh_token: 'refresh',
          expires_at: Date.now() / 1000 + 3600,
          user: {
            id: '1',
            email: '<EMAIL>',
            created_at: '2023-01-01',
            aud: 'authenticated',
            app_metadata: {},
          },
        }

        mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
          data: { session: mockSession },
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.signIn('<EMAIL>', 'password123')
        })

        expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
        expect(response.data.session).toEqual(mockSession)
        expect(response.error).toBe(null)
      })

      it('should handle sign in errors', async () => {
        const mockError = { message: 'Invalid credentials' }

        mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
          data: { session: null },
          error: mockError,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.signIn('<EMAIL>', 'wrongpassword')
        })

        expect(response.error).toEqual(mockError)
      })
    })

    describe('signOut', () => {
      it('should handle successful sign out', async () => {
        mockSupabaseClient.auth.signOut.mockResolvedValue({
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        await act(async () => {
          await result.current.signOut()
        })

        expect(mockSupabaseClient.auth.signOut).toHaveBeenCalled()
        expect(mockRouter.push).toHaveBeenCalledWith('/')
      })

      it('should handle sign out errors gracefully', async () => {
        const mockError = { message: 'Sign out failed' }
        mockSupabaseClient.auth.signOut.mockResolvedValue({
          error: mockError,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        // Should not throw even if sign out fails
        await act(async () => {
          await result.current.signOut()
        })

        expect(mockSupabaseClient.auth.signOut).toHaveBeenCalled()
      })
    })

    describe('resetPassword', () => {
      it('should handle successful password reset', async () => {
        mockSupabaseClient.auth.resetPasswordForEmail.mockResolvedValue({
          data: {},
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.resetPassword('<EMAIL>')
        })

        expect(mockSupabaseClient.auth.resetPasswordForEmail).toHaveBeenCalledWith(
          '<EMAIL>',
          {
            redirectTo: expect.stringContaining('/auth/new-password'),
          }
        )
        expect(response.error).toBe(null)
      })

      it('should handle password reset errors', async () => {
        const mockError = { message: 'User not found' }
        mockSupabaseClient.auth.resetPasswordForEmail.mockResolvedValue({
          data: null,
          error: mockError,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.resetPassword('<EMAIL>')
        })

        expect(response.error).toEqual(mockError)
      })
    })

    describe('updatePassword', () => {
      it('should handle successful password update', async () => {
        // Mock current session
        const mockSession = {
          access_token: 'token',
          user: { id: '1', email: '<EMAIL>' },
        }

        mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
          data: { session: mockSession },
          error: null,
        })

        mockSupabaseClient.auth.updateUser.mockResolvedValue({
          data: { user: mockSession.user },
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.updatePassword('oldpassword', 'newpassword123')
        })

        expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalledWith({
          email: undefined, // No current user email in test
          password: 'oldpassword',
        })
        expect(response.error).toBeDefined() // Should error due to no current user
      })
    })

    describe('resendConfirmation', () => {
      it('should handle successful confirmation resend', async () => {
        mockSupabaseClient.auth.resend.mockResolvedValue({
          data: {},
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        let response: any
        await act(async () => {
          response = await result.current.resendConfirmation('<EMAIL>')
        })

        expect(mockSupabaseClient.auth.resend).toHaveBeenCalledWith({
          type: 'signup',
          email: '<EMAIL>',
        })
        expect(response.error).toBe(null)
      })
    })

    describe('refreshSession', () => {
      it('should handle successful session refresh', async () => {
        const mockSession = {
          access_token: 'new_token',
          refresh_token: 'new_refresh',
          expires_at: Date.now() / 1000 + 3600,
          user: {
            id: '1',
            email: '<EMAIL>',
            created_at: '2023-01-01',
            aud: 'authenticated',
            app_metadata: {},
          },
        }

        mockSupabaseClient.auth.refreshSession.mockResolvedValue({
          data: { session: mockSession },
          error: null,
        })

        const { result } = renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })

        await act(async () => {
          await result.current.refreshSession()
        })

        expect(mockSupabaseClient.auth.refreshSession).toHaveBeenCalled()
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: { message: 'Network error' },
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: TestWrapper,
      })

      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })

      expect(result.current.user).toBe(null)
      expect(result.current.session).toBe(null)
    })

    it('should handle auth state change subscription errors', () => {
      mockSupabaseClient.auth.onAuthStateChange.mockImplementation(() => {
        throw new Error('Subscription failed')
      })

      // Should not throw during component mount
      expect(() => {
        renderHook(() => useAuth(), {
          wrapper: TestWrapper,
        })
      }).not.toThrow()
    })
  })

  describe('Cleanup', () => {
    it('should unsubscribe from auth changes on unmount', () => {
      const mockUnsubscribe = jest.fn()

      mockSupabaseClient.auth.onAuthStateChange.mockReturnValue({
        data: { subscription: { unsubscribe: mockUnsubscribe } },
      })

      const { unmount } = renderHook(() => useAuth(), {
        wrapper: TestWrapper,
      })

      unmount()

      expect(mockUnsubscribe).toHaveBeenCalled()
    })
  })
}) 