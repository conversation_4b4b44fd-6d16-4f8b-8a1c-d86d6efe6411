import React from 'react'
import { renderHook, act } from '@testing-library/react'
import { 
  usePasswordValidation, 
  useAuthForm,
} from '../hooks'
import { TEST_PASSWORDS, TEST_FORM_DATA } from './test-constants.helper'

// Mock the RateLimiter for hooks tests only
jest.mock('../utils', () => {
  const actual = jest.requireActual('../utils')
  return {
    ...actual,
    RateLimiter: jest.fn().mockImplementation(() => ({
      isAllowed: jest.fn(() => true),
      reset: jest.fn(),
      getRemainingTime: jest.fn(() => 0),
    })),
  }
})

jest.mock('../context', () => ({
  useAuth: () => ({
    signUp: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
    resetPassword: jest.fn(),
    updatePassword: jest.fn(),
    user: null,
    session: null,
    loading: false,
  }),
}))

describe('Auth Hooks', () => {
  describe('usePasswordValidation', () => {
    test('should initialize with empty password', () => {
      const { result } = renderHook(() => usePasswordValidation())

      expect(result.current.password).toBe('')
      expect(result.current.validation.score).toBe(0)
      expect(result.current.validation.isValid).toBe(false)
      expect(Array.isArray(result.current.validation.feedback)).toBe(true)
    })

    test('should update password and validate strength', () => {
      const { result } = renderHook(() => usePasswordValidation())

      act(() => {
        result.current.updatePassword('StrongPassword123!')
      })

      expect(result.current.password).toBe('StrongPassword123!')
      expect(result.current.validation.score).toBeGreaterThan(3)
      expect(result.current.validation.isValid).toBe(true)
    })

    test('should detect weak passwords', () => {
      const { result } = renderHook(() => usePasswordValidation())

      act(() => {
        result.current.updatePassword('weak')
      })

      expect(result.current.password).toBe('weak')
      expect(result.current.validation.score).toBeLessThan(4)
      expect(result.current.validation.isValid).toBe(false)
      expect(result.current.validation.feedback.length).toBeGreaterThan(0)
    })

    test('should allow direct password setting', () => {
      const { result } = renderHook(() => usePasswordValidation())

      act(() => {
        result.current.setPassword('SomePassword123!')
      })

      expect(result.current.password).toBe('SomePassword123!')
    })

    test('should validate password with updatePassword', () => {
      const { result } = renderHook(() => usePasswordValidation())

      act(() => {
        result.current.updatePassword('ValidPassword123!')
      })

      expect(result.current.password).toBe('ValidPassword123!')
      expect(result.current.validation.isValid).toBe(true)
    })
  })

  describe('useAuthForm', () => {
    test('should initialize with provided values', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      expect(result.current.values).toEqual(initialValues)
      expect(result.current.errors).toEqual({})
      expect(result.current.touched).toEqual({})
      expect(result.current.isValid).toBe(true)
    })

    test('should update field values', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      act(() => {
        result.current.setValue('email', '<EMAIL>')
      })

      expect(result.current.values.email).toBe('<EMAIL>')
    })

    test('should track field touched state', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      act(() => {
        result.current.setFieldTouched('email', true)
      })

      expect(result.current.touched.email).toBe(true)
    })

    test('should validate form', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      const isValid = result.current.validate()
      expect(typeof isValid).toBe('boolean')
    })

    test('should reset form to initial values', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      // Make some changes
      act(() => {
        result.current.setValue('email', '<EMAIL>')
        result.current.setFieldTouched('email', true)
      })

      expect(result.current.values.email).toBe('<EMAIL>')
      expect(result.current.touched.email).toBe(true)

      // Reset form
      act(() => {
        result.current.reset()
      })

      expect(result.current.values).toEqual(initialValues)
      expect(result.current.errors).toEqual({})
      expect(result.current.touched).toEqual({})
    })

    test('should handle multiple field updates', () => {
      const initialValues = { email: '', password: '', confirmPassword: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      act(() => {
        result.current.setValue('email', '<EMAIL>')
        result.current.setValue('password', 'NewPassword123!')
        result.current.setValue('confirmPassword', 'NewPassword123!')
      })

      expect(result.current.values.email).toBe('<EMAIL>')
      expect(result.current.values.password).toBe('NewPassword123!')
      expect(result.current.values.confirmPassword).toBe('NewPassword123!')
    })

    test('should track validity state', () => {
      const initialValues = { email: '', password: '' }
      const { result } = renderHook(() => useAuthForm(initialValues))

      expect(typeof result.current.isValid).toBe('boolean')
    })
  })
}) 