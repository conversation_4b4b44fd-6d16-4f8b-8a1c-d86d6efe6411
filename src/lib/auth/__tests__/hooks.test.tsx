import { renderHook, act } from '@testing-library/react'
import { toast } from 'sonner'
import {
  useSignUp,
  useSignIn,
  usePasswordReset,
  usePasswordUpdate,
  useNewPassword,
  usePasswordValidation,
  useAuthForm,
} from '../hooks'
import { supabaseClient } from '../../supabase/client'

// Mock the auth context
const mockAuthContext = {
  signUp: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
  resetPassword: jest.fn(),
  updatePassword: jest.fn(),
  resendConfirmation: jest.fn(),
  refreshSession: jest.fn(),
  user: null,
  session: null,
  loading: false,
}

jest.mock('../context', () => ({
  useAuth: () => mockAuthContext,
}))

// Mock supabase client
jest.mock('../../supabase/client', () => ({
  supabaseClient: {
    auth: {
      updateUser: jest.fn(),
      signInWithOAuth: jest.fn(),
      resend: jest.fn(),
      refreshSession: jest.fn(),
    },
  },
}))

// Mock toast
const mockToast = toast as jest.Mocked<typeof toast>

describe('Auth Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('useSignUp', () => {
    it('should handle successful sign up', async () => {
      mockAuthContext.signUp.mockResolvedValue({
        data: { user: { id: '1', email: '<EMAIL>' } },
        error: null,
      })

      const { result } = renderHook(() => useSignUp())

      await act(async () => {
        const response = await result.current.handleSignUp('<EMAIL>', 'Password123!')
        expect(response.success).toBe(true)
      })

      expect(mockAuthContext.signUp).toHaveBeenCalledWith('<EMAIL>', 'Password123!')
      expect(mockToast.success).toHaveBeenCalledWith(
        'Account created successfully! Please check your email to verify your account.'
      )
      expect(result.current.loading).toBe(false)
      expect(result.current.error).toBe(null)
    })

    it('should handle sign up error', async () => {
      const errorMessage = 'Email already exists'
      mockAuthContext.signUp.mockResolvedValue({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => useSignUp())

      await act(async () => {
        const response = await result.current.handleSignUp('<EMAIL>', 'Password123!')
        expect(response.success).toBe(false)
        expect(response.error).toBe(errorMessage)
      })

      expect(result.current.error).toBe(errorMessage)
      expect(mockToast.success).not.toHaveBeenCalled()
    })

    it('should set loading state correctly', async () => {
      let resolvePromise: (value: any) => void
      const promise = new Promise((resolve) => {
        resolvePromise = resolve
      })
      mockAuthContext.signUp.mockReturnValue(promise)

      const { result } = renderHook(() => useSignUp())

      act(() => {
        result.current.handleSignUp('<EMAIL>', 'Password123!')
      })

      expect(result.current.loading).toBe(true)

      await act(async () => {
        resolvePromise({ data: {}, error: null })
        await promise
      })

      expect(result.current.loading).toBe(false)
    })
  })

  describe('useSignIn', () => {
    it('should handle successful sign in', async () => {
      mockAuthContext.signIn.mockResolvedValue({
        data: { user: { id: '1', email: '<EMAIL>' } },
        error: null,
      })

      const { result } = renderHook(() => useSignIn())

      await act(async () => {
        const response = await result.current.handleSignIn('<EMAIL>', 'password')
        expect(response.success).toBe(true)
      })

      expect(mockAuthContext.signIn).toHaveBeenCalledWith('<EMAIL>', 'password')
      expect(mockToast.success).toHaveBeenCalledWith('Welcome back!')
    })

    it('should handle sign in error', async () => {
      const errorMessage = 'Invalid credentials'
      mockAuthContext.signIn.mockResolvedValue({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => useSignIn())

      await act(async () => {
        const response = await result.current.handleSignIn('<EMAIL>', 'wrongpassword')
        expect(response.success).toBe(false)
        expect(response.error).toBe(errorMessage)
      })

      expect(result.current.error).toBe(errorMessage)
    })
  })

  describe('usePasswordReset', () => {
    it('should handle successful password reset', async () => {
      mockAuthContext.resetPassword.mockResolvedValue({
        data: {},
        error: null,
      })

      const { result } = renderHook(() => usePasswordReset())

      await act(async () => {
        const response = await result.current.handlePasswordReset('<EMAIL>')
        expect(response.success).toBe(true)
      })

      expect(mockAuthContext.resetPassword).toHaveBeenCalledWith('<EMAIL>')
      expect(mockToast.success).toHaveBeenCalledWith('Password reset link sent to your email!')
      expect(result.current.success).toBe(true)
    })

    it('should handle password reset error', async () => {
      const errorMessage = 'User not found'
      mockAuthContext.resetPassword.mockResolvedValue({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => usePasswordReset())

      await act(async () => {
        const response = await result.current.handlePasswordReset('<EMAIL>')
        expect(response.success).toBe(false)
        expect(response.error).toBe(errorMessage)
      })

      expect(result.current.error).toBe(errorMessage)
      expect(result.current.success).toBe(false)
    })
  })

  describe('usePasswordUpdate', () => {
    it('should handle successful password update', async () => {
      mockAuthContext.updatePassword.mockResolvedValue({
        data: {},
        error: null,
      })

      const { result } = renderHook(() => usePasswordUpdate())

      await act(async () => {
        const response = await result.current.handlePasswordUpdate('oldpass', 'NewPass123!')
        expect(response.success).toBe(true)
      })

      expect(mockAuthContext.updatePassword).toHaveBeenCalledWith('oldpass', 'NewPass123!')
      expect(mockToast.success).toHaveBeenCalledWith('Password updated successfully!')
    })

    it('should handle password update error', async () => {
      const errorMessage = 'Current password is incorrect'
      mockAuthContext.updatePassword.mockResolvedValue({
        data: null,
        error: { message: errorMessage },
      })

      const { result } = renderHook(() => usePasswordUpdate())

      await act(async () => {
        const response = await result.current.handlePasswordUpdate('wrongpass', 'NewPass123!')
        expect(response.success).toBe(false)
        expect(response.error).toBe(errorMessage)
      })

      expect(result.current.error).toBe(errorMessage)
    })
  })

  describe('useNewPassword', () => {
    it('should handle successful new password setting', async () => {
      const mockUpdateUser = supabaseClient.auth.updateUser as jest.MockedFunction<typeof supabaseClient.auth.updateUser>
      mockUpdateUser.mockResolvedValue({
        data: {
          user: { id: '1', email: '<EMAIL>' } as any,
        },
        error: null,
      })

      const { result } = renderHook(() => useNewPassword())

      await act(async () => {
        const response = await result.current.handleNewPassword('NewPass123!')
        expect(response.success).toBe(true)
      })

      expect(mockUpdateUser).toHaveBeenCalledWith({
        password: 'NewPass123!',
      })
      expect(mockToast.success).toHaveBeenCalledWith('Password updated successfully!')
    })
  })

  describe('usePasswordValidation', () => {
    it('should validate password strength in real-time', () => {
      const { result } = renderHook(() => usePasswordValidation())

      act(() => {
        result.current.updatePassword('weak')
      })

      expect(result.current.validation).toBeDefined()
      expect(result.current.validation.isValid).toBe(false)
      expect(result.current.validation.feedback.length).toBeGreaterThan(0)

      act(() => {
        result.current.updatePassword('StrongPass123!')
      })

      expect(result.current.validation.isValid).toBe(true)
      expect(result.current.validation.feedback.length).toBe(0)
    })
  })

  describe('useAuthForm', () => {
    const mockValidationSchema = {
      safeParse: jest.fn(),
    }

    it('should handle form validation', () => {
      mockValidationSchema.safeParse.mockReturnValue({
        success: true,
        data: { email: '<EMAIL>', password: 'Password123!' },
      })

      const { result } = renderHook(() =>
        useAuthForm(
          { email: '', password: '' },
          mockValidationSchema
        )
      )

      act(() => {
        result.current.setValue('email', '<EMAIL>')
        result.current.setValue('password', 'Password123!')
      })

      expect(result.current.values.email).toBe('<EMAIL>')
      expect(result.current.values.password).toBe('Password123!')

      act(() => {
        result.current.validate()
      })

      expect(result.current.isValid).toBe(true)
    })

    it('should reset form values', () => {
      const { result } = renderHook(() =>
        useAuthForm({ email: '<EMAIL>', password: 'Password123!' })
      )

      act(() => {
        result.current.setValue('email', '<EMAIL>')
        result.current.reset()
      })

      expect(result.current.values.email).toBe('<EMAIL>')
    })
  })
}) 