// Test constants for auth module unit tests
// This follows the clean code rule of having constants for easy customization

export const TEST_USERS = {
  VALID_USER: {
    id: '1',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00Z',
    aud: 'authenticated',
    app_metadata: {},
    user_metadata: {
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON>',
      full_name: '<PERSON>',
      avatar_url: 'https://example.com/avatar.jpg',
    },
  },
  USER_WITHOUT_METADATA: {
    id: '2',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00Z',
    aud: 'authenticated',
    app_metadata: {},
    user_metadata: {},
  },
  USER_WITH_PROVIDER: {
    id: '3',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00Z',
    aud: 'authenticated',
    app_metadata: {},
    user_metadata: {
      provider: 'google',
      full_name: 'Google User',
      avatar_url: 'https://lh3.googleusercontent.com/avatar.jpg',
    },
  },
} as const

export const TEST_SESSIONS = {
  VALID_SESSION: {
    access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test',
    refresh_token: 'refresh_token_123',
    expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    user: TEST_USERS.VALID_USER,
  },
  EXPIRED_SESSION: {
    access_token: 'expired_token',
    refresh_token: 'expired_refresh',
    expires_at: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
    user: TEST_USERS.VALID_USER,
  },
} as const

export const TEST_PASSWORDS = {
  STRONG_PASSWORDS: [
    'SecurePass123!',
    'MyPassword1@',
    'TestP@ssw0rd',
    'Complex!Pass123',
    'VeryLongAndStrong123!@#',
  ],
  WEAK_PASSWORDS: [
    'weak',
    'password',
    '123456',
    'password123',
    'PASSWORD',
    'Password!',
    'Password123',
    'Short1!',
  ],
  PASSWORDS_WITH_REPEATED_CHARS: [
    'Passssss123!',
    'AAAAAAaa1!',
    'Password111!',
  ],
} as const

export const TEST_EMAILS = {
  VALID_EMAILS: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  INVALID_EMAILS: [
    'invalid.email',
    '@example.com',
    'user@',
    'user <EMAIL>',
    'user@domain',
    '',
    '<EMAIL>',
    '<EMAIL>',
  ],
} as const

export const TEST_FORM_DATA = {
  VALID_SIGNUP: {
    email: '<EMAIL>',
    password: 'SecurePass123!',
    confirmPassword: 'SecurePass123!',
    firstName: 'John',
    lastName: 'Doe',
    terms: true,
  },
  VALID_SIGNIN: {
    email: '<EMAIL>',
    password: 'password123',
    rememberMe: true,
  },
  VALID_PASSWORD_RESET: {
    email: '<EMAIL>',
  },
  VALID_PASSWORD_UPDATE: {
    password: 'NewSecurePass123!',
    confirmPassword: 'NewSecurePass123!',
  },
  VALID_CHANGE_PASSWORD: {
    currentPassword: 'OldPass123!',
    password: 'NewSecurePass123!',
    confirmPassword: 'NewSecurePass123!',
  },
  VALID_PROFILE_UPDATE: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
  },
  VALID_OTP: {
    token: '123456',
  },
} as const

export const TEST_ERRORS = {
  AUTH_ERRORS: {
    INVALID_CREDENTIALS: 'Invalid login credentials',
    EMAIL_NOT_CONFIRMED: 'Email not confirmed',
    USER_NOT_FOUND: 'User not found',
    INVALID_PASSWORD: 'Signup requires a valid password',
    USER_ALREADY_EXISTS: 'User already registered',
    WEAK_PASSWORD: 'Password should be at least 6 characters',
    INVALID_EMAIL: 'Unable to validate email address',
    TOO_MANY_REQUESTS: 'Too many requests',
    EXPIRED_TOKEN: 'Token has expired or is invalid',
    EXPIRED_LINK: 'Email link is invalid or has expired',
  },
  MAPPED_ERRORS: {
    INVALID_CREDENTIALS: 'The email or password you entered is incorrect. Please try again.',
    EMAIL_NOT_CONFIRMED: 'Please check your email and click the confirmation link before signing in.',
    USER_NOT_FOUND: 'No account found with this email address.',
    INVALID_PASSWORD: 'Please provide a valid password.',
    USER_ALREADY_EXISTS: 'An account with this email already exists. Try signing in instead.',
    WEAK_PASSWORD: 'Password must be at least 6 characters long.',
    INVALID_EMAIL: 'Please enter a valid email address.',
    TOO_MANY_REQUESTS: 'Too many attempts. Please wait a moment before trying again.',
    EXPIRED_TOKEN: 'This session has expired. Please sign in again.',
    EXPIRED_LINK: 'This link has expired. Please request a new password reset.',
  },
} as const

export const TEST_RATE_LIMITS = {
  SIGNIN_LIMIT: 5, // 5 attempts per 15 minutes
  SIGNIN_WINDOW: 15 * 60 * 1000, // 15 minutes
  SIGNUP_LIMIT: 3, // 3 attempts per hour  
  SIGNUP_WINDOW: 60 * 60 * 1000, // 1 hour
  EMAIL_CONFIRMATION_LIMIT: 3, // 3 attempts per hour
  EMAIL_CONFIRMATION_WINDOW: 60 * 60 * 1000, // 1 hour
} as const

export const TEST_VALIDATION_MESSAGES = {
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters',
  PASSWORD_TOO_LONG: 'Password must be less than 128 characters',
  PASSWORD_NO_UPPERCASE: 'Password must contain at least one uppercase letter',
  PASSWORD_NO_LOWERCASE: 'Password must contain at least one lowercase letter',
  PASSWORD_NO_NUMBER: 'Password must contain at least one number',
  PASSWORD_NO_SPECIAL: 'Password must contain at least one special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  EMAIL_REQUIRED: 'Please enter a valid email address',
  EMAIL_TOO_LONG: 'Email must be less than 255 characters',
  FIRSTNAME_REQUIRED: 'First name is required',
  FIRSTNAME_TOO_LONG: 'First name must be less than 50 characters',
  LASTNAME_REQUIRED: 'Last name is required',
  LASTNAME_TOO_LONG: 'Last name must be less than 50 characters',
  TERMS_REQUIRED: 'You must agree to the terms and conditions',
  PASSWORD_REQUIRED: 'Password is required',
  CURRENT_PASSWORD_REQUIRED: 'Current password is required',
  OTP_INVALID_LENGTH: 'OTP must be 6 digits',
  OTP_INVALID_FORMAT: 'OTP must contain only numbers',
} as const

export const TEST_URLS = {
  AUTH_CALLBACK: '/auth/callback',
  NEW_PASSWORD: '/auth/new-password',
  SIGN_IN: '/auth/sign-in',
  SIGN_UP: '/auth/sign-up',
  RESET_PASSWORD: '/auth/reset-password',
  HOME: '/',
  PROFILE: '/profile',
} as const

export const TEST_COOKIE_OPTIONS = {
  PRODUCTION: {
    secure: true,
    sameSite: 'strict' as const,
    path: '/',
  },
  DEVELOPMENT: {
    secure: false,
    sameSite: 'strict' as const,
    path: '/',
  },
} as const

export const TEST_OAUTH_PROVIDERS = ['google', 'github', 'discord'] as const

export const TEST_TIMEOUTS = {
  SHORT: 100,
  MEDIUM: 1000,
  LONG: 5000,
  SESSION_REFRESH: 30 * 60 * 1000, // 30 minutes
} as const 