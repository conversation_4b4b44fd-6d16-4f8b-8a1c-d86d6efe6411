import React from 'react'
import { renderHook } from '@testing-library/react'
import { AuthProvider, useAuth } from '../context'

// Mock Supabase and other dependencies
jest.mock('../../supabase/client', () => ({
  supabaseClient: {
    auth: {
      getSession: jest.fn(() => Promise.resolve({ data: { session: null } })),
      getUser: jest.fn(() => Promise.resolve({ data: { user: null } })),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      })),
    },
  },
}))

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}))

describe('AuthContext', () => {
  describe('AuthProvider Structure', () => {
    test('should provide all required auth methods and state', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      )

      const { result } = renderHook(() => useAuth(), { wrapper })

      // Check all required methods exist
      expect(typeof result.current.signUp).toBe('function')
      expect(typeof result.current.signIn).toBe('function')
      expect(typeof result.current.signOut).toBe('function')
      expect(typeof result.current.resetPassword).toBe('function')
      expect(typeof result.current.updatePassword).toBe('function')
      expect(typeof result.current.resendConfirmation).toBe('function')
      expect(typeof result.current.refreshSession).toBe('function')

      // Check state properties
      expect(result.current.user).toBeDefined()
      expect(result.current.session).toBeDefined()
      expect(typeof result.current.loading).toBe('boolean')
    })

    test('should throw error when used outside provider', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        renderHook(() => useAuth())
      }).toThrow('useAuth must be used within an AuthProvider')

      consoleSpy.mockRestore()
    })

    test('should have initial loading state', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      )

      const { result } = renderHook(() => useAuth(), { wrapper })

      // Loading should be boolean
      expect(typeof result.current.loading).toBe('boolean')
    })
  })


}) 