import {
  passwordSchema,
  emailSchema,
  signUpSchema,
  signInSchema,
  resetPasswordSchema,
  updatePasswordSchema,
  changePasswordSchema,
  updateProfileSchema,
  otpSchema,
} from '../validation'

describe('Auth Validation', () => {
  describe('passwordSchema', () => {
    it('should accept valid passwords', () => {
      const validPasswords = [
        'SecurePass123!',
        'MyPassword1@',
        'TestP@ssw0rd',
        'Complex!Pass123'
      ]

      validPasswords.forEach(password => {
        expect(passwordSchema.safeParse(password).success).toBe(true)
      })
    })

    it('should reject passwords that are too short', () => {
      const result = passwordSchema.safeParse('Short1!')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Password must be at least 8 characters')
      }
    })

    it('should reject passwords that are too long', () => {
      const longPassword = 'a'.repeat(129) + 'A1!'
      const result = passwordSchema.safeParse(longPassword)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors[0].message).toBe('Password must be less than 128 characters')
      }
    })

    it('should reject passwords without uppercase letters', () => {
      const result = passwordSchema.safeParse('password123!')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message.includes('uppercase'))).toBe(true)
      }
    })

    it('should reject passwords without lowercase letters', () => {
      const result = passwordSchema.safeParse('PASSWORD123!')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message.includes('lowercase'))).toBe(true)
      }
    })

    it('should reject passwords without numbers', () => {
      const result = passwordSchema.safeParse('Password!')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message.includes('number'))).toBe(true)
      }
    })

    it('should reject passwords without special characters', () => {
      const result = passwordSchema.safeParse('Password123')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message.includes('special'))).toBe(true)
      }
    })
  })

  describe('emailSchema', () => {
    it('should accept valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]

      validEmails.forEach(email => {
        expect(emailSchema.safeParse(email).success).toBe(true)
      })
    })

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid.email',
        '@example.com',
        'user@',
        'user <EMAIL>',
        'user@domain',
        ''
      ]

      invalidEmails.forEach(email => {
        expect(emailSchema.safeParse(email).success).toBe(false)
      })
    })

    it('should convert email to lowercase', () => {
      const result = emailSchema.safeParse('<EMAIL>')
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toBe('<EMAIL>')
      }
    })

    it('should reject emails that are too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com'
      const result = emailSchema.safeParse(longEmail)
      expect(result.success).toBe(false)
    })
  })

  describe('signUpSchema', () => {
    const validSignUpData = {
      email: '<EMAIL>',
      password: 'SecurePass123!',
      confirmPassword: 'SecurePass123!',
      firstName: 'John',
      lastName: 'Doe',
      terms: true
    }

    it('should accept valid sign-up data', () => {
      const result = signUpSchema.safeParse(validSignUpData)
      expect(result.success).toBe(true)
    })

    it('should reject when passwords do not match', () => {
      const data = {
        ...validSignUpData,
        confirmPassword: 'DifferentPass123!'
      }
      const result = signUpSchema.safeParse(data)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message === 'Passwords do not match')).toBe(true)
      }
    })

    it('should reject when terms are not accepted', () => {
      const data = {
        ...validSignUpData,
        terms: false
      }
      const result = signUpSchema.safeParse(data)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.errors.some(e => e.message.includes('terms'))).toBe(true)
      }
    })

    it('should accept data without optional firstName and lastName', () => {
      const data = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!',
        terms: true
      }
      const result = signUpSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should reject names that are too long', () => {
      const data = {
        ...validSignUpData,
        firstName: 'a'.repeat(51)
      }
      const result = signUpSchema.safeParse(data)
      expect(result.success).toBe(false)
    })
  })

  describe('signInSchema', () => {
    it('should accept valid sign-in data', () => {
      const data = {
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: true
      }
      const result = signInSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should accept data without rememberMe', () => {
      const data = {
        email: '<EMAIL>',
        password: 'password123'
      }
      const result = signInSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should reject empty password', () => {
      const data = {
        email: '<EMAIL>',
        password: ''
      }
      const result = signInSchema.safeParse(data)
      expect(result.success).toBe(false)
    })
  })

  describe('resetPasswordSchema', () => {
    it('should accept valid email', () => {
      const result = resetPasswordSchema.safeParse({ email: '<EMAIL>' })
      expect(result.success).toBe(true)
    })

    it('should reject invalid email', () => {
      const result = resetPasswordSchema.safeParse({ email: 'invalid-email' })
      expect(result.success).toBe(false)
    })
  })

  describe('updatePasswordSchema', () => {
    it('should accept matching passwords', () => {
      const data = {
        password: 'NewSecurePass123!',
        confirmPassword: 'NewSecurePass123!'
      }
      const result = updatePasswordSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should reject non-matching passwords', () => {
      const data = {
        password: 'NewSecurePass123!',
        confirmPassword: 'DifferentPass123!'
      }
      const result = updatePasswordSchema.safeParse(data)
      expect(result.success).toBe(false)
    })
  })

  describe('changePasswordSchema', () => {
    it('should accept valid change password data', () => {
      const data = {
        currentPassword: 'OldPass123!',
        password: 'NewSecurePass123!',
        confirmPassword: 'NewSecurePass123!'
      }
      const result = changePasswordSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should reject empty current password', () => {
      const data = {
        currentPassword: '',
        password: 'NewSecurePass123!',
        confirmPassword: 'NewSecurePass123!'
      }
      const result = changePasswordSchema.safeParse(data)
      expect(result.success).toBe(false)
    })
  })

  describe('updateProfileSchema', () => {
    it('should accept valid profile data', () => {
      const data = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      }
      const result = updateProfileSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should accept profile data without email', () => {
      const data = {
        firstName: 'John',
        lastName: 'Doe'
      }
      const result = updateProfileSchema.safeParse(data)
      expect(result.success).toBe(true)
    })

    it('should reject empty names', () => {
      const data = {
        firstName: '',
        lastName: 'Doe'
      }
      const result = updateProfileSchema.safeParse(data)
      expect(result.success).toBe(false)
    })
  })

  describe('otpSchema', () => {
    it('should accept valid 6-digit OTP', () => {
      const result = otpSchema.safeParse({ token: '123456' })
      expect(result.success).toBe(true)
    })

    it('should reject OTP with wrong length', () => {
      const result = otpSchema.safeParse({ token: '12345' })
      expect(result.success).toBe(false)
    })

    it('should reject OTP with non-numeric characters', () => {
      const result = otpSchema.safeParse({ token: '12345a' })
      expect(result.success).toBe(false)
    })
  })
}) 