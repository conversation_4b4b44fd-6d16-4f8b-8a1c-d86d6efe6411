import { supabaseClient } from './client';

export const uploadDappLogo = async (file: File, filename: string) => {
  try {
    const { data, error } = await supabaseClient.storage
      .from('dapp-logos')
      .upload(filename, file, {
        cacheControl: '3600',
        upsert: true,
      });

    if (error) {
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error: 'Upload failed' };
  }
};

export const getDappLogoUrl = (path: string) => {
  const { data } = supabaseClient.storage
    .from('dapp-logos')
    .getPublicUrl(path);
  
  return data.publicUrl;
};

export const deleteDappLogo = async (path: string) => {
  try {
    const { data, error } = await supabaseClient.storage
      .from('dapp-logos')
      .remove([path]);

    if (error) {
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error: 'Delete failed' };
  }
};
