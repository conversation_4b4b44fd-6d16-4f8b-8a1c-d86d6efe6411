import { createServerClient } from "@supabase/ssr";
import { type ReadonlyRequestCookies } from "next/dist/server/web/spec-extension/adapters/request-cookies";
import { SUPABASE_ENV, validateSupabaseEnv } from "@/constants/supabase";

// Validate required environment variables
validateSupabaseEnv();

export function serverClient(cookieStore: ReadonlyRequestCookies) {
  return createServerClient(
    SUPABASE_ENV.URL!,
    SUPABASE_ENV.ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll().map(({ name, value }) => ({ name, value }));
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            try {
              cookieStore.set(name, value, options);
            } catch (e) {
            }
          });
        },
      },
    }
  );
}
