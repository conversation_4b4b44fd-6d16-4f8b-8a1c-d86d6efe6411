import { createClient } from "@supabase/supabase-js";
import { SUPABASE_ENV, SUPABASE_CONFIG, validateServiceRoleKey } from "@/constants/supabase";

// Check if service role key is available
const hasServiceRole = validateServiceRoleKey();

// Create service role client only if key is available, otherwise use anon key
const serviceRoleSupabase = hasServiceRole 
  ? createClient(
      SUPABASE_ENV.URL!,
      SUPABASE_ENV.SERVICE_ROLE_KEY!,
      SUPABASE_CONFIG.SERVICE_ROLE
    )
  : createClient(
      SUPABASE_ENV.URL!,
      SUPABASE_ENV.ANON_KEY!,
      SUPABASE_CONFIG.CLIENT
    );

export { serviceRoleSupabase };
