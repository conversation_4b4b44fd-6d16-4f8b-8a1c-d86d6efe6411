// This file is deprecated - use @/constants/supabase instead
import { createClient } from '@supabase/supabase-js';
import { 
  SUPABASE_ENV, 
  SUPABASE_ENDPOINTS, 
  SUPABASE_HEADERS,
  validateSupabaseEnv 
} from '@/constants/supabase';

// Validate environment variables
validateSupabaseEnv();

// Legacy exports for backward compatibility
export const SUPABASE_URL = SUPABASE_ENV.URL!;
export const SUPABASE_ANON_KEY = SUPABASE_ENV.ANON_KEY!;

// Create Supabase client using new constants
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Legacy endpoint exports (mapped to new constants)
export const SUPABASE_AUTH_ENDPOINTS = SUPABASE_ENDPOINTS.AUTH;
export const SUPABASE_REST_ENDPOINTS = SUPABASE_ENDPOINTS.REST;

// Legacy header exports (mapped to new constants)
export { SUPABASE_HEADERS } from '@/constants/supabase';
export const SUPABASE_AUTH_HEADERS = SUPABASE_HEADERS.AUTH;
export const SUPABASE_REST_HEADERS = SUPABASE_HEADERS.BASE;
